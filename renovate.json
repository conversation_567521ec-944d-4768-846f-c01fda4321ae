{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "enabledManagers": ["custom.regex", "kustomize"], "automerge": false, "separateMinorPatch": true, "labels": ["dependencies"], "schedule": "before 5am every weekday", "customManagers": [{"description": "Match dependencies in .tools_versions.yaml that are properly annotated with `# renovate: datasource={} depName={}.`", "customType": "regex", "fileMatch": ["\\.tools_versions\\.yaml$"], "matchStrings": ["# renovate: datasource=(?<datasource>.*?) depName=(?<depName>.*?)\\n.+\"(?<currentValue>.*?)\""]}, {"description": "Match dependencies in .custom-gcl.yml that are properly annotated with `# renovate: datasource={} depName={}.`", "customType": "regex", "fileMatch": ["\\.custom-gcl\\.yml$"], "matchStrings": ["# renovate: datasource=(?<datasource>.*?) depName=(?<depName>.*?)\\nversion: v(?<currentValue>.*)"]}], "packageRules": [{"packageNames": ["kubernetes-sigs/kustomize"], "extractVersion": "^kustomize\\/v(?<version>\\d+\\.\\d+\\.\\d+)$"}]}