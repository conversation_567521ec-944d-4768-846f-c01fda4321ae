---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller-incubator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongservicefacades.incubator.ingress-controller.konghq.com
spec:
  group: incubator.ingress-controller.konghq.com
  names:
    categories:
    - kong-ingress-controller
    kind: KongServiceFacade
    listKind: KongServiceFacadeList
    plural: kongservicefacades
    singular: kongservicefacade
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          KongServiceFacade allows creating separate Kong Services for a single Kubernetes
          Service. It can be used as Kubernetes Ingress' backend (via its path's `backend.resource`
          field). It's designed to enable creating two "virtual" Services in Kong that will point
          to the same Kubernetes Service, but will have different configuration (e.g. different
          set of plugins, different load balancing algorithm, etc.).

          KongServiceFacade requires `kubernetes.io/ingress.class` annotation with a value
          matching the ingressClass of the Kong Ingress Controller (`kong` by default) to be reconciled.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongServiceFacadeSpec defines the desired state of KongServiceFacade.
            properties:
              backendRef:
                description: |-
                  Backend is a reference to a Kubernetes Service that is used as a backend
                  for this Kong Service Facade.
                properties:
                  name:
                    description: Name is the name of the referenced Kubernetes Service.
                    type: string
                  port:
                    description: Port is the port of the referenced Kubernetes Service.
                    format: int32
                    type: integer
                required:
                - name
                - port
                type: object
            required:
            - backendRef
            type: object
          status:
            description: KongServiceFacadeStatus defines the observed state of KongServiceFacade.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongServiceFacade.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
