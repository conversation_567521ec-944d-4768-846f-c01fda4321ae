---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongplugins.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongPlugin
    listKind: KongPluginList
    plural: kongplugins
    shortNames:
    - kp
    singular: kongplugin
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Name of the plugin
      jsonPath: .plugin
      name: Plugin-Type
      type: string
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Indicates if the plugin is disabled
      jsonPath: .disabled
      name: Disabled
      priority: 1
      type: boolean
    - description: Configuration of the plugin
      jsonPath: .config
      name: Config
      priority: 1
      type: string
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1
    schema:
      openAPIV3Schema:
        description: KongPlugin is the Schema for the kongplugins API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          config:
            description: |-
              Config contains the plugin configuration. It's a list of keys and values
              required to configure the plugin.
              Please read the documentation of the plugin being configured to set values
              in here. For any plugin in Kong, anything that goes in the `config` JSON
              key in the Admin API request, goes into this property.
              Only one of `config` or `configFrom` may be used in a KongPlugin, not both at once.
            type: object
            x-kubernetes-preserve-unknown-fields: true
          configFrom:
            description: |-
              ConfigFrom references a secret containing the plugin configuration.
              This should be used when the plugin configuration contains sensitive information,
              such as AWS credentials in the Lambda plugin or the client secret in the OIDC plugin.
              Only one of `config` or `configFrom` may be used in a KongPlugin, not both at once.
            properties:
              secretKeyRef:
                description: Specifies a name and a key of a secret to refer to. The
                  namespace is implicitly set to the one of referring object.
                properties:
                  key:
                    description: The key containing the value.
                    type: string
                  name:
                    description: The secret containing the key.
                    type: string
                required:
                - key
                - name
                type: object
            required:
            - secretKeyRef
            type: object
          configPatches:
            description: |-
              ConfigPatches represents JSON patches to the configuration of the plugin.
              Each item means a JSON patch to add something in the configuration,
              where path is specified in `path` and value is in `valueFrom` referencing
              a key in a secret.
              When Config is specified, patches will be applied to the configuration in Config.
              Otherwise, patches will be applied to an empty object.
            items:
              description: |-
                ConfigPatch is a JSON patch (RFC6902) to add values from Secret to the generated configuration.
                It is an equivalent of the following patch:
                `{"op": "add", "path": {.Path}, "value": {.ComputedValueFrom}}`.
              properties:
                path:
                  description: Path is the JSON-Pointer value (RFC6901) that references
                    a location within the target configuration.
                  type: string
                valueFrom:
                  description: ValueFrom is the reference to a key of a secret where
                    the patched value comes from.
                  properties:
                    secretKeyRef:
                      description: Specifies a name and a key of a secret to refer
                        to. The namespace is implicitly set to the one of referring
                        object.
                      properties:
                        key:
                          description: The key containing the value.
                          type: string
                        name:
                          description: The secret containing the key.
                          type: string
                      required:
                      - key
                      - name
                      type: object
                  required:
                  - secretKeyRef
                  type: object
              required:
              - path
              - valueFrom
              type: object
            type: array
          consumerRef:
            description: ConsumerRef is a reference to a particular consumer.
            type: string
          disabled:
            description: Disabled set if the plugin is disabled or not.
            type: boolean
          instance_name:
            description: |-
              InstanceName is an optional custom name to identify an instance of the plugin. This is useful when running the
              same plugin in multiple contexts, for example, on multiple services.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          ordering:
            description: |-
              Ordering overrides the normal plugin execution order. It's only available on Kong Enterprise.
              `<phase>` is a request processing phase (for example, `access` or `body_filter`) and
              `<plugin>` is the name of the plugin that will run before or after the KongPlugin.
              For example, a KongPlugin with `plugin: rate-limiting` and `before.access: ["key-auth"]`
              will create a rate limiting plugin that limits requests _before_ they are authenticated.
            properties:
              after:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
              before:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: PluginOrderingPhase indicates which plugins in a phase
                  should affect the target plugin's order
                type: object
            type: object
          plugin:
            description: PluginName is the name of the plugin to which to apply the
              config.
            type: string
          protocols:
            description: |-
              Protocols configures plugin to run on requests received on specific
              protocols.
            items:
              description: |-
                KongProtocol is a valid Kong protocol.
                This alias is necessary to deal with https://github.com/kubernetes-sigs/controller-tools/issues/342
              enum:
              - http
              - https
              - grpc
              - grpcs
              - tcp
              - tls
              - udp
              type: string
            type: array
          run_on:
            description: |-
              RunOn configures the plugin to run on the first or the second or both
              nodes in case of a service mesh deployment.
            enum:
            - first
            - second
            - all
            type: string
          status:
            description: Status represents the current status of the KongPlugin resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KongPluginStatus.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
            type: object
        required:
        - plugin
        type: object
        x-kubernetes-validations:
        - message: Using both config and configFrom fields is not allowed.
          rule: '!(has(self.config) && has(self.configFrom))'
        - message: Using both configFrom and configPatches fields is not allowed.
          rule: '!(has(self.configFrom) && has(self.configPatches))'
        - message: The plugin field is immutable
          rule: self.plugin == oldSelf.plugin
    served: true
    storage: true
    subresources:
      status: {}
