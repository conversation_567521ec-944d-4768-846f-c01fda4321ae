---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konglicenses.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongLicense
    listKind: KongLicenseList
    plural: konglicenses
    shortNames:
    - kl
    singular: konglicense
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - description: Enabled to configure on Kong gateway instances
      jsonPath: .enabled
      name: Enabled
      type: boolean
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KongLicense stores a Kong enterprise license to apply to managed
          Kong gateway instances.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          enabled:
            default: true
            description: |-
              Enabled is set to true to let controllers (like KIC or KGO) to reconcile it.
              Default value is true to apply the license by default.
            type: boolean
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          rawLicenseString:
            description: RawLicenseString is a string with the raw content of the
              license.
            type: string
          status:
            description: Status is the status of the KongLicense being processed by
              controllers.
            properties:
              controllers:
                items:
                  description: |-
                    KongLicenseControllerStatus is the status of owning KongLicense being processed
                    identified by the controllerName field.
                  properties:
                    conditions:
                      default:
                      - lastTransitionTime: "1970-01-01T00:00:00Z"
                        message: Waiting for controller
                        reason: Pending
                        status: Unknown
                        type: Programmed
                      description: Conditions describe the current conditions of the
                        KongLicense on the controller.
                      items:
                        description: Condition contains details for one aspect of
                          the current state of this API Resource.
                        properties:
                          lastTransitionTime:
                            description: |-
                              lastTransitionTime is the last time the condition transitioned from one status to another.
                              This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                            format: date-time
                            type: string
                          message:
                            description: |-
                              message is a human readable message indicating details about the transition.
                              This may be an empty string.
                            maxLength: 32768
                            type: string
                          observedGeneration:
                            description: |-
                              observedGeneration represents the .metadata.generation that the condition was set based upon.
                              For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                              with respect to the current state of the instance.
                            format: int64
                            minimum: 0
                            type: integer
                          reason:
                            description: |-
                              reason contains a programmatic identifier indicating the reason for the condition's last transition.
                              Producers of specific condition types may define expected values and meanings for this field,
                              and whether the values are considered a guaranteed API.
                              The value should be a CamelCase string.
                              This field may not be empty.
                            maxLength: 1024
                            minLength: 1
                            pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                            type: string
                          status:
                            description: status of the condition, one of True, False,
                              Unknown.
                            enum:
                            - "True"
                            - "False"
                            - Unknown
                            type: string
                          type:
                            description: type of condition in CamelCase or in foo.example.com/CamelCase.
                            maxLength: 316
                            pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                            type: string
                        required:
                        - lastTransitionTime
                        - message
                        - reason
                        - status
                        - type
                        type: object
                      maxItems: 8
                      type: array
                      x-kubernetes-list-map-keys:
                      - type
                      x-kubernetes-list-type: map
                    controllerName:
                      description: |-
                        ControllerName is an identifier of the controller to reconcile this KongLicense.
                        Should be unique in the list of controller statuses.
                      type: string
                    controllerRef:
                      description: |-
                        ControllerRef is the reference of the controller to reconcile this KongLicense.
                        It is usually the name of (KIC/KGO) pod that reconciles it.
                      properties:
                        group:
                          description: |-
                            Group is the group of referent.
                            It should be empty if the referent is in "core" group (like pod).
                          maxLength: 253
                          pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                          type: string
                        kind:
                          description: |-
                            Kind is the kind of the referent.
                            By default the nil kind means kind Pod.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                          type: string
                        name:
                          description: Name is the name of the referent.
                          maxLength: 253
                          minLength: 1
                          type: string
                        namespace:
                          description: |-
                            Namespace is the namespace of the referent.
                            It should be empty if the referent is cluster scoped.
                          maxLength: 63
                          minLength: 1
                          pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                          type: string
                      required:
                      - name
                      type: object
                  required:
                  - controllerName
                  type: object
                type: array
                x-kubernetes-list-map-keys:
                - controllerName
                x-kubernetes-list-type: map
            type: object
        required:
        - enabled
        - rawLicenseString
        type: object
    served: true
    storage: true
    subresources:
      status: {}
