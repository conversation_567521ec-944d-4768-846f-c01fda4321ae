---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konnectapiauthconfigurations.konnect.konghq.com
spec:
  group: konnect.konghq.com
  names:
    categories:
    - kong
    kind: KonnectAPIAuthConfiguration
    listKind: KonnectAPIAuthConfigurationList
    plural: konnectapiauthconfigurations
    singular: konnectapiauthconfiguration
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The API authentication information is valid
      jsonPath: .status.conditions[?(@.type=='APIAuthValid')].status
      name: Valid
      type: string
    - description: Konnect Organization ID this API authentication configuration belongs
        to.
      jsonPath: .status.organizationID
      name: OrgID
      type: string
    - description: Configured server URL.
      jsonPath: .status.serverURL
      name: ServerURL
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KonnectAPIAuthConfiguration is the Schema for the Konnect configuration
          type.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the specification of the KonnectAPIAuthConfiguration
              resource.
            properties:
              secretRef:
                description: |-
                  SecretRef is a reference to a Kubernetes Secret containing the Konnect token.
                  This secret is required to have the konghq.com/credential label set to "konnect".
                properties:
                  name:
                    description: name is unique within a namespace to reference a
                      secret resource.
                    type: string
                  namespace:
                    description: namespace defines the space within which the secret
                      name must be unique.
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              serverURL:
                description: |-
                  ServerURL is the URL of the Konnect server.
                  It can be either a full URL with an HTTPs scheme or just a hostname.
                  Please refer to https://docs.konghq.com/konnect/network/ for the list of supported hostnames.
                type: string
                x-kubernetes-validations:
                - message: Server URL is required
                  rule: size(self) > 0
                - message: Server URL is immutable
                  rule: self == oldSelf
                - message: Server URL must use HTTPs if specifying scheme
                  rule: 'isURL(self) ? url(self).getScheme() == ''https'' : true'
                - message: Server URL must satisfy hostname (RFC 1123) regex if not
                    a valid absolute URL
                  rule: 'size(self) > 0 && !isURL(self) ? self.matches(''^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]).)*([A-Za-z0-9]|[A-Za-z0-9][A-Za-z0-9-]*[A-Za-z0-9])$'')
                    : true'
              token:
                description: Token is the Konnect token used to authenticate with
                  the Konnect API.
                type: string
              type:
                description: KonnectAPIAuthType is the type of authentication used
                  to authenticate with the Konnect API.
                enum:
                - token
                - secretRef
                type: string
            required:
            - serverURL
            - type
            type: object
            x-kubernetes-validations:
            - message: spec.token is required if auth type is set to token
              rule: 'self.type == ''token'' ? size(self.token) > 0 : true'
            - message: spec.secretRef is required if auth type is set to secretRef
              rule: 'self.type == ''secretRef'' ? has(self.secretRef) : true'
            - message: spec.token and spec.secretRef cannot be set at the same time
              rule: '!(has(self.token) && has(self.secretRef))'
          status:
            description: Status is the status of the KonnectAPIAuthConfiguration resource.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Valid
                description: Conditions describe the status of the Konnect configuration.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                minItems: 1
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              organizationID:
                description: OrganizationID is the unique identifier of the organization
                  in Konnect.
                type: string
              serverURL:
                description: ServerURL is configured server URL.
                type: string
            type: object
        type: object
        x-kubernetes-validations:
        - message: Konnect tokens have to start with spat_ or kpat_
          rule: self.spec.type != 'token' || (self.spec.token.startsWith('spat_')
            || self.spec.token.startsWith('kpat_'))
        - message: Token is required once set
          rule: self.spec.type != 'token' || (!has(oldSelf.spec.token) || has(self.spec.token))
    served: true
    storage: true
    subresources:
      status: {}
