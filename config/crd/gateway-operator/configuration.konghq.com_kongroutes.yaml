---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongroutes.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong
    kind: KongRoute
    listKind: KongRouteList
    plural: kongroutes
    singular: kongroute
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is Programmed on Konnect
      jsonPath: .status.conditions[?(@.type=='Programmed')].status
      name: Programmed
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KongRoute is the schema for Routes API which defines a Kong Route.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongRouteSpec defines spec of a Kong Route.
            properties:
              controlPlaneRef:
                description: |-
                  ControlPlaneRef is a reference to a ControlPlane this KongRoute is associated with.
                  Route can either specify a ControlPlaneRef and be 'serviceless' route or
                  specify a ServiceRef and be associated with a Service.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              destinations:
                description: A list of IP destinations of incoming connections that
                  match this Route when using stream routing. Each entry is an object
                  with fields "ip" (optionally in CIDR range notation) and/or "port".
                items:
                  properties:
                    ip:
                      type: string
                    port:
                      format: int64
                      type: integer
                  type: object
                type: array
              headers:
                additionalProperties:
                  items:
                    type: string
                  type: array
                description: 'One or more lists of values indexed by header name that
                  will cause this Route to match if present in the request. The `Host`
                  header cannot be used with this attribute: hosts should be specified
                  using the `hosts` attribute. When `headers` contains only one value
                  and that value starts with the special prefix `~*`, the value is
                  interpreted as a regular expression.'
                type: object
              hosts:
                description: A list of domain names that match this Route. Note that
                  the hosts value is case sensitive.
                items:
                  type: string
                type: array
              https_redirect_status_code:
                description: 'The status code Kong responds with when all properties
                  of a Route match except the protocol i.e. if the protocol of the
                  request is `HTTP` instead of `HTTPS`. `Location` header is injected
                  by Kong if the field is set to 301, 302, 307 or 308. Note: This
                  config applies only if the Route is configured to only accept the
                  `https` protocol.'
                format: int64
                type: integer
              methods:
                description: A list of HTTP methods that match this Route.
                items:
                  type: string
                type: array
              name:
                description: The name of the Route. Route names must be unique, and
                  they are case sensitive. For example, there can be two different
                  Routes named "test" and "Test".
                type: string
              path_handling:
                description: Controls how the Service path, Route path and requested
                  path are combined when sending a request to the upstream. See above
                  for a detailed description of each behavior.
                type: string
              paths:
                description: A list of paths that match this Route.
                items:
                  type: string
                type: array
              preserve_host:
                description: When matching a Route via one of the `hosts` domain names,
                  use the request `Host` header in the upstream request headers. If
                  set to `false`, the upstream `Host` header will be that of the Service's
                  `host`.
                type: boolean
              protocols:
                description: An array of the protocols this Route should allow. See
                  KongRoute for a list of accepted protocols. When set to only `"https"`,
                  HTTP requests are answered with an upgrade error. When set to only
                  `"http"`, HTTPS requests are answered with an error.
                items:
                  type: string
                type: array
              regex_priority:
                description: A number used to choose which route resolves a given
                  request when several routes match it using regexes simultaneously.
                  When two routes match the path and have the same `regex_priority`,
                  the older one (lowest `created_at`) is used. Note that the priority
                  for non-regex routes is different (longer non-regex routes are matched
                  before shorter ones).
                format: int64
                type: integer
              request_buffering:
                description: Whether to enable request body buffering or not. With
                  HTTP 1.1, it may make sense to turn this off on services that receive
                  data with chunked transfer encoding.
                type: boolean
              response_buffering:
                description: Whether to enable response body buffering or not. With
                  HTTP 1.1, it may make sense to turn this off on services that send
                  data with chunked transfer encoding.
                type: boolean
              serviceRef:
                description: |-
                  ServiceRef is a reference to a Service this KongRoute is associated with.
                  Route can either specify a ControlPlaneRef and be 'serviceless' route or
                  specify a ServiceRef and be associated with a Service.
                properties:
                  namespacedRef:
                    description: NamespacedRef is a reference to a KongService.
                    properties:
                      name:
                        description: Name is the name of the entity.
                        minLength: 1
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    description: |-
                      Type can be one of:
                      - namespacedRef
                    enum:
                    - namespacedRef
                    type: string
                type: object
                x-kubernetes-validations:
                - message: when type is namespacedRef, namespacedRef must be set
                  rule: 'self.type == ''namespacedRef'' ? has(self.namespacedRef)
                    : true'
              snis:
                description: A list of SNIs that match this Route when using stream
                  routing.
                items:
                  type: string
                type: array
              sources:
                description: A list of IP sources of incoming connections that match
                  this Route when using stream routing. Each entry is an object with
                  fields "ip" (optionally in CIDR range notation) and/or "port".
                items:
                  properties:
                    ip:
                      type: string
                    port:
                      format: int64
                      type: integer
                  type: object
                type: array
              strip_path:
                description: When matching a Route via one of the `paths`, strip the
                  matching prefix from the upstream request URL.
                type: boolean
              tags:
                description: An optional set of strings associated with the Route
                  for grouping and filtering.
                items:
                  type: string
                maxItems: 20
                type: array
                x-kubernetes-validations:
                - message: tags entries must not be longer than 128 characters
                  rule: self.all(tag, size(tag) >= 1 && size(tag) <= 128)
            type: object
            x-kubernetes-validations:
            - message: KIC is not supported as control plane
              rule: '!has(self.controlPlaneRef) ? true : self.controlPlaneRef.type
                != ''kic'''
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Programmed
            description: KongRouteStatus represents the current status of the Kong
              Route resource.
            properties:
              conditions:
                description: Conditions describe the status of the Konnect entity.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                minItems: 1
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this entity is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                  serviceID:
                    description: ServiceID is the Konnect ID of the Service this entity
                      is associated with.
                    type: string
                type: object
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: If protocols has 'http', at least one of 'hosts', 'methods', 'paths'
            or 'headers' must be set
          rule: 'has(self.spec.protocols) && self.spec.protocols.exists(p, p == ''http'')
            ? (has(self.spec.hosts) || has(self.spec.methods) || has(self.spec.paths)
            || has(self.spec.paths) || has(self.spec.paths) || has(self.spec.headers)
            ) : true'
        - message: spec.controlPlaneRef cannot specify namespace for namespaced resource
          rule: '(!has(self.spec.controlPlaneRef) || !has(self.spec.controlPlaneRef.konnectNamespacedRef))
            ? true : !has(self.spec.controlPlaneRef.konnectNamespacedRef.__namespace__)'
        - message: controlPlaneRef is required once set
          rule: '!has(oldSelf.spec.controlPlaneRef) || has(self.spec.controlPlaneRef)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!has(self.spec) || !has(self.spec.controlPlaneRef)) ? true : (!has(self.status)
            || !self.status.conditions.exists(c, c.type == ''Programmed'' && c.status
            == ''True'')) ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
    served: true
    storage: true
    subresources:
      status: {}
