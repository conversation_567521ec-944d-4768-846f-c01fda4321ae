---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongpluginbindings.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong
    kind: KongPluginBinding
    listKind: KongPluginBindingList
    plural: kongpluginbindings
    singular: kongpluginbinding
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Kind of the plugin
      jsonPath: .spec.pluginRef.kind
      name: Plugin-kind
      type: string
    - description: Name of the plugin
      jsonPath: .spec.pluginRef.name
      name: Plugin-name
      type: string
    - description: The Resource is Programmed
      jsonPath: .status.conditions[?(@.type=='Programmed')].status
      name: Programmed
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KongPluginBinding is the schema for Plugin Bindings API which
          defines a Kong Plugin Binding.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongPluginBindingSpec defines specification of a KongPluginBinding.
            properties:
              controlPlaneRef:
                description: ControlPlaneRef is a reference to a ControlPlane this
                  KongPluginBinding is associated with.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              pluginRef:
                description: PluginReference is a reference to the KongPlugin or KongClusterPlugin
                  resource.
                properties:
                  kind:
                    default: KongPlugin
                    description: Kind can be KongPlugin or KongClusterPlugin. If not
                      set, it is assumed to be KongPlugin.
                    enum:
                    - KongPlugin
                    - KongClusterPlugin
                    type: string
                  name:
                    description: Name is the name of the KongPlugin or KongClusterPlugin
                      resource.
                    type: string
                required:
                - name
                type: object
                x-kubernetes-validations:
                - message: pluginRef name must be set
                  rule: self.name != ''
              scope:
                default: OnlyTargets
                description: Scope defines the scope of the plugin binding.
                enum:
                - OnlyTargets
                - GlobalInControlPlane
                type: string
              targets:
                description: |-
                  Targets contains the targets references. It is possible to set multiple combinations
                  of references, as described in https://docs.konghq.com/gateway/latest/key-concepts/plugins/#precedence
                  The complete set of allowed combinations and their order of precedence for plugins
                  configured to multiple entities is:

                  1. Consumer + route + service
                  2. Consumer group + service + route
                  3. Consumer + route
                  4. Consumer + service
                  5. Consumer group + route
                  6. Consumer group + service
                  7. Route + service
                  8. Consumer
                  9. Consumer group
                  10. Route
                  11. Service
                properties:
                  consumerGroupRef:
                    description: |-
                      ConsumerGroupReference is used to reference a configuration.konghq.com/ConsumerGroup resource.
                      The group/kind is fixed, therefore the reference is performed only by name.
                    properties:
                      name:
                        description: Name is the name of the entity.
                        type: string
                    required:
                    - name
                    type: object
                  consumerRef:
                    description: |-
                      ConsumerReference is used to reference a configuration.konghq.com/Consumer resource.
                      The group/kind is fixed, therefore the reference is performed only by name.
                    properties:
                      name:
                        description: Name is the name of the entity.
                        type: string
                    required:
                    - name
                    type: object
                  routeRef:
                    description: |-
                      RouteReference can be used to reference one of the following resouces:
                      - networking.k8s.io/Ingress
                      - gateway.networking.k8s.io/HTTPRoute
                      - gateway.networking.k8s.io/GRPCRoute
                      - configuration.konghq.com/KongRoute
                    properties:
                      group:
                        enum:
                        - ""
                        - core
                        - networking.k8s.io
                        - gateway.networking.k8s.io
                        - configuration.konghq.com
                        type: string
                      kind:
                        enum:
                        - Service
                        - Ingress
                        - HTTPRoute
                        - GRPCRoute
                        - KongService
                        - KongRoute
                        type: string
                      name:
                        description: Name is the name of the entity.
                        type: string
                    required:
                    - group
                    - kind
                    - name
                    type: object
                    x-kubernetes-validations:
                    - message: group/kind not allowed for the routeRef
                      rule: (self.kind == 'KongRoute' && self.group == 'configuration.konghq.com')
                        || (self.kind == 'Ingress' && self.group == 'networking.k8s.io')
                        || (self.kind == 'HTTPRoute' && self.group == 'gateway.networking.k8s.io')
                        || (self.kind == 'GRPCRoute' && self.group == 'gateway.networking.k8s.io')
                  serviceRef:
                    description: |-
                      ServiceReference can be used to reference one of the following resouces:
                      - core/Service or /Service
                      - configuration.konghq.com/KongService
                    properties:
                      group:
                        enum:
                        - ""
                        - core
                        - networking.k8s.io
                        - gateway.networking.k8s.io
                        - configuration.konghq.com
                        type: string
                      kind:
                        enum:
                        - Service
                        - Ingress
                        - HTTPRoute
                        - GRPCRoute
                        - KongService
                        - KongRoute
                        type: string
                      name:
                        description: Name is the name of the entity.
                        type: string
                    required:
                    - group
                    - kind
                    - name
                    type: object
                    x-kubernetes-validations:
                    - message: group/kind not allowed for the serviceRef
                      rule: (self.kind == 'KongService' && self.group == 'configuration.konghq.com')
                        || (self.kind == 'Service' && (self.group == '' || self.group
                        == 'core'))
                type: object
                x-kubernetes-validations:
                - message: Cannot set Consumer and ConsumerGroup at the same time
                  rule: '(has(self.consumerRef) ? !has(self.consumerGroupRef) : true)'
                - message: KongRoute can be used only when serviceRef is unset or
                    set to KongService
                  rule: '(has(self.routeRef) && self.routeRef.kind == ''KongRoute'')
                    ? (!has(self.serviceRef) || self.serviceRef.kind == ''KongService'')
                    : true'
                - message: KongService can be used only when routeRef is unset or
                    set to KongRoute
                  rule: '(has(self.serviceRef) && self.serviceRef.kind == ''KongService'')
                    ? (!has(self.routeRef) || self.routeRef.kind == ''KongRoute'')
                    : true'
            required:
            - controlPlaneRef
            - pluginRef
            type: object
            x-kubernetes-validations:
            - message: At least one target reference must be set when scope is 'OnlyTargets'
              rule: 'self.scope == ''OnlyTargets'' ? has(self.targets) && (has(self.targets.routeRef)
                || has(self.targets.serviceRef) || has(self.targets.consumerRef) ||
                has(self.targets.consumerGroupRef)) : true'
            - message: No targets must be set when scope is 'GlobalInControlPlane'
              rule: 'self.scope == ''GlobalInControlPlane'' ? !has(self.targets) :
                true'
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Programmed
            description: KongPluginBindingStatus represents the current status of
              the KongBinding resource.
            properties:
              conditions:
                description: Conditions describe the status of the Konnect entity.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                minItems: 1
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this Route is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                type: object
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: controlPlaneRef is required once set
          rule: '!has(oldSelf.spec.controlPlaneRef) || has(self.spec.controlPlaneRef)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!self.status.conditions.exists(c, c.type == ''Programmed'' && c.status
            == ''True'')) ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
        - message: spec.controlPlaneRef cannot specify namespace for namespaced resource
          rule: '(!has(self.spec) || !has(self.spec.controlPlaneRef) || !has(self.spec.controlPlaneRef.konnectNamespacedRef))
            ? true : !has(self.spec.controlPlaneRef.konnectNamespacedRef.__namespace__)'
    served: true
    storage: true
    subresources:
      status: {}
