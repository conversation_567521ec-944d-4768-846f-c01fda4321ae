---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: watchnamespacegrants.gateway-operator.konghq.com
spec:
  group: gateway-operator.konghq.com
  names:
    kind: WatchNamespaceGrant
    listKind: WatchNamespaceGrantList
    plural: watchnamespacegrants
    singular: watchnamespacegrant
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          WatchNamespaceGrant is a grant that allows a trusted namespace to watch
          resources in the namespace this grant exists in.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec is the desired state of the WatchNamespaceGrant.
            properties:
              from:
                description: |-
                  From describes the trusted namespaces and kinds that can reference the
                  namespace this grant exists in.
                items:
                  description: WatchNamespaceGrantFrom describes trusted namespaces.
                  properties:
                    group:
                      description: Group is the group of the referent.
                      enum:
                      - gateway-operator.konghq.com
                      type: string
                    kind:
                      description: Kind is the kind of the referent.
                      enum:
                      - ControlPlane
                      type: string
                    namespace:
                      description: Namespace is the namespace of the referent.
                      type: string
                  required:
                  - group
                  - kind
                  - namespace
                  type: object
                maxItems: 16
                minItems: 1
                type: array
            required:
            - from
            type: object
        type: object
    served: true
    storage: true
