---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongupstreams.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong
    kind: KongUpstream
    listKind: KongUpstreamList
    plural: kongupstreams
    singular: kongupstream
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is Programmed on Konnect
      jsonPath: .status.conditions[?(@.type=='Programmed')].status
      name: Programmed
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KongUpstream is the schema for Upstream API which defines a Kong
          Upstream.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongUpstreamSpec defines the spec of Kong Upstream.
            properties:
              algorithm:
                description: Which load balancing algorithm to use.
                type: string
              client_certificate:
                description: If set, the certificate to be used as client certificate
                  while TLS handshaking to the upstream server.
                properties:
                  id:
                    type: string
                type: object
              controlPlaneRef:
                description: ControlPlaneRef is a reference to a ControlPlane this
                  KongUpstream is associated with.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              hash_fallback:
                description: What to use as hashing input if the primary `hash_on`
                  does not return a hash (eg. header is missing, or no Consumer identified).
                  Not available if `hash_on` is set to `cookie`.
                type: string
              hash_fallback_header:
                description: The header name to take the value from as hash input.
                  Only required when `hash_fallback` is set to `header`.
                type: string
              hash_fallback_query_arg:
                description: The name of the query string argument to take the value
                  from as hash input. Only required when `hash_fallback` is set to
                  `query_arg`.
                type: string
              hash_fallback_uri_capture:
                description: The name of the route URI capture to take the value from
                  as hash input. Only required when `hash_fallback` is set to `uri_capture`.
                type: string
              hash_on:
                description: What to use as hashing input. Using `none` results in
                  a weighted-round-robin scheme with no hashing.
                type: string
              hash_on_cookie:
                description: The cookie name to take the value from as hash input.
                  Only required when `hash_on` or `hash_fallback` is set to `cookie`.
                  If the specified cookie is not in the request, Kong will generate
                  a value and set the cookie in the response.
                type: string
              hash_on_cookie_path:
                description: The cookie path to set in the response headers. Only
                  required when `hash_on` or `hash_fallback` is set to `cookie`.
                type: string
              hash_on_header:
                description: The header name to take the value from as hash input.
                  Only required when `hash_on` is set to `header`.
                type: string
              hash_on_query_arg:
                description: The name of the query string argument to take the value
                  from as hash input. Only required when `hash_on` is set to `query_arg`.
                type: string
              hash_on_uri_capture:
                description: The name of the route URI capture to take the value from
                  as hash input. Only required when `hash_on` is set to `uri_capture`.
                type: string
              healthchecks:
                properties:
                  active:
                    properties:
                      concurrency:
                        format: int64
                        type: integer
                      headers:
                        additionalProperties:
                          type: string
                        type: object
                      healthy:
                        properties:
                          http_statuses:
                            items:
                              format: int64
                              type: integer
                            type: array
                          interval:
                            type: number
                          successes:
                            format: int64
                            type: integer
                        type: object
                      http_path:
                        type: string
                      https_sni:
                        type: string
                      https_verify_certificate:
                        type: boolean
                      timeout:
                        type: number
                      type:
                        type: string
                      unhealthy:
                        properties:
                          http_failures:
                            format: int64
                            type: integer
                          http_statuses:
                            items:
                              format: int64
                              type: integer
                            type: array
                          interval:
                            type: number
                          tcp_failures:
                            format: int64
                            type: integer
                          timeouts:
                            format: int64
                            type: integer
                        type: object
                    type: object
                  passive:
                    properties:
                      healthy:
                        properties:
                          http_statuses:
                            items:
                              format: int64
                              type: integer
                            type: array
                          successes:
                            format: int64
                            type: integer
                        type: object
                      type:
                        type: string
                      unhealthy:
                        properties:
                          http_failures:
                            format: int64
                            type: integer
                          http_statuses:
                            items:
                              format: int64
                              type: integer
                            type: array
                          tcp_failures:
                            format: int64
                            type: integer
                          timeouts:
                            format: int64
                            type: integer
                        type: object
                    type: object
                  threshold:
                    type: number
                type: object
              host_header:
                description: The hostname to be used as `Host` header when proxying
                  requests through Kong.
                type: string
              name:
                description: This is a hostname, which must be equal to the `host`
                  of a Service.
                type: string
              slots:
                description: The number of slots in the load balancer algorithm. If
                  `algorithm` is set to `round-robin`, this setting determines the
                  maximum number of slots. If `algorithm` is set to `consistent-hashing`,
                  this setting determines the actual number of slots in the algorithm.
                  Accepts an integer in the range `10`-`65536`.
                format: int64
                maximum: 65536
                minimum: 10
                type: integer
              tags:
                description: An optional set of strings associated with the Upstream
                  for grouping and filtering.
                items:
                  type: string
                maxItems: 20
                type: array
                x-kubernetes-validations:
                - message: tags entries must not be longer than 128 characters
                  rule: self.all(tag, size(tag) >= 1 && size(tag) <= 128)
              use_srv_name:
                description: If set, the balancer will use SRV hostname(if DNS Answer
                  has SRV record) as the proxy upstream `Host`.
                type: boolean
            required:
            - controlPlaneRef
            type: object
            x-kubernetes-validations:
            - message: KIC is not supported as control plane
              rule: '!has(self.controlPlaneRef) ? true : self.controlPlaneRef.type
                != ''kic'''
            - message: hash_fallback_header is required when `hash_fallback` is set
                to `header`.
              rule: '!has(self.hash_fallback) || (self.hash_fallback != ''header''
                || has(self.hash_fallback_header))'
            - message: hash_fallback_query_arg is required when `hash_fallback` is
                set to `query_arg`.
              rule: '!has(self.hash_fallback) || (self.hash_fallback != ''query_arg''
                || has(self.hash_fallback_query_arg))'
            - message: hash_fallback_uri_capture is required when `hash_fallback`
                is set to `uri_capture`.
              rule: '!has(self.hash_fallback) || (self.hash_fallback != ''uri_capture''
                || has(self.hash_fallback_uri_capture))'
            - message: hash_on_cookie is required when hash_fallback is set to `cookie`.
              rule: '!has(self.hash_fallback) || (self.hash_fallback != ''cookie''
                || has(self.hash_on_cookie))'
            - message: hash_on_cookie is required when hash_on is set to `cookie`.
              rule: '!has(self.hash_on) || (self.hash_on != ''cookie'' || has(self.hash_on_cookie))'
            - message: hash_on_cookie_path is required when hash_fallback is set to
                `cookie`.
              rule: '!has(self.hash_fallback) || (self.hash_fallback != ''cookie''
                || has(self.hash_on_cookie_path))'
            - message: hash_on_cookie_path is required when hash_on is set to `cookie`.
              rule: '!has(self.hash_on) || (self.hash_on != ''cookie'' || has(self.hash_on_cookie_path))'
            - message: hash_on_header is required when hash_on is set to `header`.
              rule: '!has(self.hash_on) || (self.hash_on != ''header'' || has(self.hash_on_header))'
            - message: hash_on_query_arg is required when `hash_on` is set to `query_arg`.
              rule: '!has(self.hash_on) || (self.hash_on != ''query_arg'' || has(self.hash_on_query_arg))'
            - message: hash_on_uri_capture is required when `hash_on` is set to `uri_capture`.
              rule: '!has(self.hash_on) || (self.hash_on != ''uri_capture'' || has(self.hash_on_uri_capture))'
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Programmed
            description: KongUpstreamStatus represents the current status of the Kong
              Upstream resource.
            properties:
              conditions:
                description: Conditions describe the status of the Konnect entity.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                minItems: 1
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this Route is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                type: object
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: controlPlaneRef is required once set
          rule: '!has(oldSelf.spec.controlPlaneRef) || has(self.spec.controlPlaneRef)'
        - message: spec.controlPlaneRef cannot specify namespace for namespaced resource
          rule: '(!has(self.spec.controlPlaneRef) || !has(self.spec.controlPlaneRef.konnectNamespacedRef))
            ? true : !has(self.spec.controlPlaneRef.konnectNamespacedRef.__namespace__)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!self.status.conditions.exists(c, c.type == ''Programmed'' && c.status
            == ''True'')) ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
    served: true
    storage: true
    subresources:
      status: {}
