---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konnectcloudgatewaynetworks.konnect.konghq.com
spec:
  group: konnect.konghq.com
  names:
    categories:
    - kong
    kind: KonnectCloudGatewayNetwork
    listKind: KonnectCloudGatewayNetworkList
    plural: konnectcloudgatewaynetworks
    singular: konnectcloudgatewaynetwork
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is Programmed on Konnect
      jsonPath: .status.conditions[?(@.type=='Programmed')].status
      name: Programmed
      type: string
    - description: The state the network is in
      jsonPath: .status.state
      name: State
      type: string
    - description: Konnect ID
      jsonPath: .status.id
      name: ID
      type: string
    - description: Konnect Organization ID this resource belongs to.
      jsonPath: .status.organizationID
      name: OrgID
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KonnectCloudGatewayNetwork is the Schema for the Konnect Network
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the desired state of KonnectCloudGatewayNetwork.
            properties:
              availability_zones:
                description: List of availability zones that the network is attached
                  to.
                items:
                  type: string
                maxItems: 5
                minItems: 1
                type: array
              cidr_block:
                description: CIDR block configuration for the network.
                type: string
              cloud_gateway_provider_account_id:
                description: Specifies the provider Account ID.
                type: string
              konnect:
                description: KonnectConfiguration is the Schema for the KonnectConfiguration
                  API.
                properties:
                  authRef:
                    description: |-
                      APIAuthConfigurationRef is the reference to the API Auth Configuration
                      that should be used for this Konnect Configuration.
                    properties:
                      name:
                        description: Name is the name of the KonnectAPIAuthConfiguration
                          resource.
                        type: string
                    required:
                    - name
                    type: object
                required:
                - authRef
                type: object
              name:
                description: Specifies the name of the network on Konnect.
                type: string
              region:
                description: Region ID for cloud provider region.
                type: string
              state:
                description: Initial state for creating a network.
                type: string
            required:
            - availability_zones
            - cidr_block
            - cloud_gateway_provider_account_id
            - konnect
            - name
            - region
            type: object
          status:
            description: Status defines the observed state of KonnectCloudGatewayNetwork.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KonnectCloudGatewayNetwork.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              id:
                description: |-
                  ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                  If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                type: string
              organizationID:
                description: OrgID is ID of Konnect Org that this entity has been
                  created in.
                type: string
              serverURL:
                description: ServerURL is the URL of the Konnect server in which the
                  entity exists.
                type: string
              state:
                description: State is the current state of the network. Can be e.g.
                  initializing, ready, terminating.
                type: string
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: spec.name is immutable when an entity is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : oldSelf.spec.name ==
            self.spec.name'
        - message: spec.cloud_gateway_provider_account_id is immutable when an entity
            is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : oldSelf.spec.cloud_gateway_provider_account_id
            == self.spec.cloud_gateway_provider_account_id'
        - message: spec.region is immutable when an entity is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : oldSelf.spec.region
            == self.spec.region'
        - message: spec.availability_zones is immutable when an entity is already
            Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : oldSelf.spec.availability_zones
            == self.spec.availability_zones'
        - message: spec.cidr_block is immutable when an entity is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : oldSelf.spec.cidr_block
            == self.spec.cidr_block'
        - message: spec.state is immutable when an entity is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : (!has(self.spec.state)
            && !has(oldSelf.spec.state)) || self.spec.state == oldSelf.spec.state'
    served: true
    storage: true
    subresources:
      status: {}
