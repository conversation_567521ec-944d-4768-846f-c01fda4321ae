---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: dataplanemetricsextensions.gateway-operator.konghq.com
spec:
  group: gateway-operator.konghq.com
  names:
    categories:
    - kong
    - all
    kind: DataPlaneMetricsExtension
    listKind: DataPlaneMetricsExtensionList
    plural: dataplanemetricsextensions
    singular: dataplanemetricsextension
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          DataPlaneMetricsExtension holds the configuration for the DataPlane metrics extension.
          It can be attached to a ControlPlane using its spec.extensions.
          When attached it will make the ControlPlane configure its DataPlane with
          the specified metrics configuration.
          Additionally, it will also make the operator expose DataPlane's metrics
          enriched with metadata required for in-cluster Kubernetes autoscaling.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: DataPlaneMetricsExtensionSpec defines the spec for the DataPlaneMetricsExtension.
            properties:
              config:
                description: Config holds the configuration for the DataPlane metrics.
                properties:
                  bandwidth:
                    default: false
                    description: |-
                      Bandwidth indicates whether bandwidth metrics are enabled for the DataPlane.
                      This translates into deployed instances having `bandwidth_metrics` option set
                      on the Prometheus plugin.
                    type: boolean
                  latency:
                    default: false
                    description: |-
                      Latency indicates whether latency metrics are enabled for the DataPlane.
                      This translates into deployed instances having `latency_metrics` option set
                      on the Prometheus plugin.
                    type: boolean
                  statusCode:
                    default: false
                    description: |-
                      StatusCode indicates whether status code metrics are enabled for the DataPlane.
                      This translates into deployed instances having `status_code_metrics` option set
                      on the Prometheus plugin.
                    type: boolean
                  upstreamHealth:
                    default: false
                    description: |-
                      UpstreamHealth indicates whether upstream health metrics are enabled for the DataPlane.
                      This translates into deployed instances having `upstream_health_metrics` option set
                      on the Prometheus plugin.
                    type: boolean
                required:
                - bandwidth
                - latency
                - statusCode
                - upstreamHealth
                type: object
              serviceSelector:
                description: |-
                  ServiceSelector holds the service selector specifying the services
                  for which metrics should be collected.
                properties:
                  matchNames:
                    description: MatchNames holds the list of Services names to match.
                    items:
                      description: ServiceSelectorEntry holds the name of a service
                        to match.
                      properties:
                        name:
                          description: Name is the name of the service to match.
                          type: string
                      required:
                      - name
                      type: object
                    type: array
                    x-kubernetes-list-map-keys:
                    - name
                    x-kubernetes-list-type: map
                required:
                - matchNames
                type: object
            required:
            - serviceSelector
            type: object
          status:
            description: DataPlaneMetricsExtensionStatus defines the status of the
              DataPlaneMetricsExtension.
            properties:
              controlPlaneRef:
                description: |-
                  ControlPlaneRef is a reference to the ControlPlane that this is associated with.
                  This field is set by the operator when this extension is associated with
                  a ControlPlane through its extensions spec.
                  There can only be one ControlPlane associated with a given DataPlaneMetricsExtension.
                  When this is unset it means that the association has been removed.
                properties:
                  name:
                    description: Name is the name of the referred resource.
                    maxLength: 253
                    minLength: 1
                    type: string
                  namespace:
                    description: |-
                      Namespace is the namespace of the referred resource.

                      For namespace-scoped resources if no Namespace is provided then the
                      namespace of the parent object MUST be used.

                      This field MUST not be set when referring to cluster-scoped resources.
                    type: string
                required:
                - name
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
