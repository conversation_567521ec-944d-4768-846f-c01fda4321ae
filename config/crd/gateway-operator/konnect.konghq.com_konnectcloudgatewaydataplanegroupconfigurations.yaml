---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konnectcloudgatewaydataplanegroupconfigurations.konnect.konghq.com
spec:
  group: konnect.konghq.com
  names:
    categories:
    - kong
    kind: KonnectCloudGatewayDataPlaneGroupConfiguration
    listKind: KonnectCloudGatewayDataPlaneGroupConfigurationList
    plural: konnectcloudgatewaydataplanegroupconfigurations
    singular: konnectcloudgatewaydataplanegroupconfiguration
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is Programmed on Konnect
      jsonPath: .status.conditions[?(@.type=='Programmed')].status
      name: Programmed
      type: string
    - description: Konnect ID
      jsonPath: .status.id
      name: ID
      type: string
    - description: ControlPlane ID
      jsonPath: .status.controlPlaneID
      name: ControlPlaneID
      type: string
    - description: Konnect Organization ID this resource belongs to.
      jsonPath: .status.organizationID
      name: OrgID
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KonnectCloudGatewayDataPlaneGroupConfiguration is the Schema
          for the Konnect Network API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the desired state of KonnectCloudGatewayDataPlaneGroupConfiguration.
            properties:
              api_access:
                default: private+public
                description: APIAccess is the desired type of API access for data-plane
                  groups.
                enum:
                - private
                - public
                - private+public
                type: string
              controlPlaneRef:
                description: |-
                  ControlPlaneRef is a reference to a ControlPlane which DataPlanes from this
                  configuration will connect to.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              dataplane_groups:
                description: |-
                  DataplaneGroups is a list of desired data-plane groups that describe where
                  to deploy instances, along with how many instances.
                items:
                  description: KonnectConfigurationDataPlaneGroup is the schema for
                    the KonnectConfiguration type.
                  properties:
                    autoscale:
                      description: Autoscale configuration for the data-plane group.
                      properties:
                        autopilot:
                          description: Autopilot specifies the autoscale configuration
                            for the data-plane group.
                          properties:
                            base_rps:
                              description: Base number of requests per second that
                                the deployment target should support.
                              format: int64
                              type: integer
                            max_rps:
                              description: Max number of requests per second that
                                the deployment target should support. If not set,
                                this defaults to 10x base_rps.
                              format: int64
                              type: integer
                          required:
                          - base_rps
                          type: object
                        static:
                          description: Static specifies the static configuration for
                            the data-plane group.
                          properties:
                            instance_type:
                              description: |-
                                Instance type name to indicate capacity.
                                Currently supported values are small, medium, large but this list might be
                                expanded in the future.
                                For all the allowed values, please refer to the Konnect API documentation
                                at https://docs.konghq.com/konnect/api/cloud-gateways/latest/#/Data-Plane%20Group%20Configurations/create-configuration.
                              type: string
                            requested_instances:
                              description: Number of data-planes the deployment target
                                will contain.
                              format: int64
                              type: integer
                          required:
                          - instance_type
                          - requested_instances
                          type: object
                        type:
                          description: Type of autoscaling to use.
                          enum:
                          - static
                          - autopilot
                          type: string
                      required:
                      - type
                      type: object
                      x-kubernetes-validations:
                      - message: can't provide both autopilot and static configuration
                        rule: '!(has(self.autopilot) && has(self.static))'
                      - message: static is required when type is static
                        rule: 'self.type == ''static'' ? has(self.static) : true'
                      - message: autopilot is required when type is autopilot
                        rule: 'self.type == ''autopilot'' ? has(self.autopilot) :
                          true'
                    environment:
                      description: Array of environment variables to set for a data-plane
                        group.
                      items:
                        description: ConfigurationDataPlaneGroupEnvironmentField specifies
                          an environment variable field for the data-plane group.
                        properties:
                          name:
                            description: Name of the environment variable field to
                              set for the data-plane group. Must be prefixed by KONG_.
                            maxLength: 63
                            minLength: 1
                            pattern: ^KONG_.
                            type: string
                          value:
                            description: Value assigned to the environment variable
                              field for the data-plane group.
                            maxLength: 63
                            minLength: 1
                            type: string
                        required:
                        - name
                        - value
                        type: object
                      type: array
                    networkRef:
                      description: |-
                        NetworkRef is the reference to the network that this data-plane group will be deployed on.

                        Cross namespace references are not supported for networkRef of type namespacedRef.
                        This will be enforced in the future but currently (due to limitation in CEL validation
                        in Kubernetes 1.31 and older) it is not.
                      properties:
                        konnectID:
                          description: |-
                            KonnectID is the schema for the KonnectID type.
                            This field is required when the Type is konnectID.
                          type: string
                        namespacedRef:
                          description: |-
                            NamespacedRef is a reference to a KeySet entity inside the cluster.
                            This field is required when the Type is namespacedRef.
                          properties:
                            name:
                              description: Name is the name of the referred resource.
                              maxLength: 253
                              minLength: 1
                              type: string
                            namespace:
                              description: |-
                                Namespace is the namespace of the referred resource.

                                For namespace-scoped resources if no Namespace is provided then the
                                namespace of the parent object MUST be used.

                                This field MUST not be set when referring to cluster-scoped resources.
                              type: string
                          required:
                          - name
                          type: object
                        type:
                          description: |-
                            Type defines type of the object which is referenced. It can be one of:

                            - konnectID
                            - namespacedRef
                          enum:
                          - konnectID
                          - namespacedRef
                          type: string
                      required:
                      - type
                      type: object
                      x-kubernetes-validations:
                      - message: when type is namespacedRef, namespacedRef must be
                          set
                        rule: 'self.type == ''namespacedRef'' ? has(self.namespacedRef)
                          : true'
                      - message: when type is namespacedRef, konnectID must not be
                          set
                        rule: 'self.type == ''namespacedRef'' ? !has(self.konnectID)
                          : true'
                      - message: when type is konnectID, konnectID must be set
                        rule: 'self.type == ''konnectID'' ? has(self.konnectID) :
                          true'
                      - message: when type is konnectID, namespacedRef must not be
                          set
                        rule: 'self.type == ''konnectID'' ? !has(self.namespacedRef)
                          : true'
                    provider:
                      description: Name of cloud provider.
                      type: string
                    region:
                      description: Region for cloud provider region.
                      type: string
                  required:
                  - autoscale
                  - networkRef
                  - provider
                  - region
                  type: object
                type: array
              version:
                description: Version specifies the desired Kong Gateway version.
                type: string
            required:
            - controlPlaneRef
            - version
            type: object
          status:
            description: Status defines the observed state of KonnectCloudGatewayDataPlaneGroupConfiguration.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KonnectCloudGatewayDataPlaneGroupConfiguration.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              controlPlaneID:
                description: ControlPlaneID is the Konnect ID of the ControlPlane
                  this Route is associated with.
                type: string
              dataplane_groups:
                description: DataPlaneGroups is a list of deployed data-plane groups.
                items:
                  description: KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup
                    defines the observed state of a deployed data-plane group.
                  properties:
                    cloud_gateway_network_id:
                      description: CloudGatewayNetworkID is the ID of the cloud gateway
                        network.
                      type: string
                    egress_ip_addresses:
                      description: EgressIPAddresses is a list of egress IP addresses
                        for the network that this data-plane group runs on.
                      items:
                        type: string
                      type: array
                    id:
                      description: ID is the ID of the deployed data-plane group.
                      type: string
                    private_ip_addresses:
                      description: PrivateIPAddresses is a list of private IP addresses
                        of the internal load balancer that proxies traffic to this
                        data-plane group.
                      items:
                        type: string
                      type: array
                    provider:
                      description: Name of cloud provider.
                      type: string
                    region:
                      description: Region ID for cloud provider region.
                      type: string
                    state:
                      description: State is the current state of the data plane group.
                        Can be e.g. initializing, ready, terminating.
                      type: string
                  required:
                  - cloud_gateway_network_id
                  - id
                  - provider
                  - region
                  - state
                  type: object
                type: array
              id:
                description: |-
                  ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                  If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                type: string
              organizationID:
                description: OrgID is ID of Konnect Org that this entity has been
                  created in.
                type: string
              serverURL:
                description: ServerURL is the URL of the Konnect server in which the
                  entity exists.
                type: string
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
