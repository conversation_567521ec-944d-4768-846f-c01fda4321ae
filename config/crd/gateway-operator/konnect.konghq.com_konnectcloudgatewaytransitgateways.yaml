---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: konnectcloudgatewaytransitgateways.konnect.konghq.com
spec:
  group: konnect.konghq.com
  names:
    categories:
    - kong
    kind: KonnectCloudGatewayTransitGateway
    listKind: KonnectCloudGatewayTransitGatewayList
    plural: konnectcloudgatewaytransitgateways
    singular: konnectcloudgatewaytransitgateway
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is Programmed on Konnect
      jsonPath: .status.conditions[?(@.type=='Programmed')].status
      name: Programmed
      type: string
    - description: The state the transit gateway is in
      jsonPath: .status.state
      name: State
      type: string
    - description: Konnect ID
      jsonPath: .status.id
      name: ID
      type: string
    - description: Network ID
      jsonPath: .status.networkID
      name: NetworkID
      type: string
    - description: Konnect Organization ID this resource belongs to.
      jsonPath: .status.organizationID
      name: OrgID
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: KonnectCloudGatewayTransitGateway is the Schema for the Konnect
          Transit Gateway API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Spec defines the desired state of KonnectCloudGatewayTransitGateway.
            properties:
              awsTransitGateway:
                description: |-
                  AWSTransitGateway is the configuration of an AWS transit gateway.
                  Used when type is "AWS Transit Gateway".
                properties:
                  attachment_config:
                    description: configuration to attach to AWS transit gateway on
                      the AWS side.
                    properties:
                      ram_share_arn:
                        description: RAMShareArn is the resource share ARN to verify
                          request to create transit gateway attachment.
                        type: string
                      transit_gateway_id:
                        description: TransitGatewayID is the AWS transit gateway ID
                          to create attachment to.
                        type: string
                    required:
                    - ram_share_arn
                    - transit_gateway_id
                    type: object
                  cidr_blocks:
                    description: |-
                      CIDR blocks for constructing a route table for the transit gateway, when attaching to the owning
                      network.
                    items:
                      type: string
                    type: array
                  dns_config:
                    description: |-
                      List of mappings from remote DNS server IP address sets to proxied internal domains, for a transit gateway
                      attachment.
                    items:
                      description: TransitGatewayDNSConfig is the DNS configuration
                        of a tansit gateway.
                      properties:
                        domain_proxy_list:
                          description: |-
                            DomainProxyList is the list of internal domain names to proxy for DNS resolution from the listed remote DNS server IP addresses,
                            for a transit gateway.
                          items:
                            type: string
                          type: array
                        remote_dns_server_ip_addresses:
                          description: RemoteDNSServerIPAddresses is the list of remote
                            DNS server IP Addresses to connect to for resolving internal
                            DNS via a transit gateway.
                          items:
                            type: string
                          type: array
                      type: object
                    type: array
                  name:
                    description: Human-readable name of the transit gateway.
                    maxLength: 120
                    minLength: 1
                    type: string
                required:
                - attachment_config
                - cidr_blocks
                - name
                type: object
              azureTransitGateway:
                description: |-
                  AzureTransitGateway is the configuration of an Azure transit gateway.
                  Used when type is "Azure Transit Gateway".
                properties:
                  attachment_config:
                    description: configuration to attach to Azure VNET peering gateway.
                    properties:
                      resource_group_name:
                        description: ResourceGroupName is the resource group name
                          for the Azure VNET Peering attachment.
                        type: string
                      subscription_id:
                        description: SubscriptionID is the subscription ID for the
                          Azure VNET Peering attachment.
                        type: string
                      tenant_id:
                        description: TenantID is the tenant ID for the Azure VNET
                          Peering attachment.
                        type: string
                      vnet_name:
                        description: VnetName is the VNET Name for the Azure VNET
                          Peering attachment.
                        type: string
                    required:
                    - resource_group_name
                    - subscription_id
                    - tenant_id
                    - vnet_name
                    type: object
                  dns_config:
                    description: |-
                      List of mappings from remote DNS server IP address sets to proxied internal domains, for a transit gateway
                      attachment.
                    items:
                      description: TransitGatewayDNSConfig is the DNS configuration
                        of a tansit gateway.
                      properties:
                        domain_proxy_list:
                          description: |-
                            DomainProxyList is the list of internal domain names to proxy for DNS resolution from the listed remote DNS server IP addresses,
                            for a transit gateway.
                          items:
                            type: string
                          type: array
                        remote_dns_server_ip_addresses:
                          description: RemoteDNSServerIPAddresses is the list of remote
                            DNS server IP Addresses to connect to for resolving internal
                            DNS via a transit gateway.
                          items:
                            type: string
                          type: array
                      type: object
                    type: array
                  name:
                    description: Human-readable name of the transit gateway.
                    maxLength: 120
                    minLength: 1
                    type: string
                required:
                - attachment_config
                - name
                type: object
              networkRef:
                description: NetworkRef is the schema for the NetworkRef type.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    type: string
                  namespacedRef:
                    description: |-
                      NamespacedRef is a reference to a KeySet entity inside the cluster.
                      This field is required when the Type is namespacedRef.
                    properties:
                      name:
                        description: Name is the name of the referred resource.
                        maxLength: 253
                        minLength: 1
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace of the referred resource.

                          For namespace-scoped resources if no Namespace is provided then the
                          namespace of the parent object MUST be used.

                          This field MUST not be set when referring to cluster-scoped resources.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    description: |-
                      Type defines type of the object which is referenced. It can be one of:

                      - konnectID
                      - namespacedRef
                    enum:
                    - konnectID
                    - namespacedRef
                    type: string
                required:
                - type
                type: object
                x-kubernetes-validations:
                - message: when type is namespacedRef, namespacedRef must be set
                  rule: 'self.type == ''namespacedRef'' ? has(self.namespacedRef)
                    : true'
                - message: when type is namespacedRef, konnectID must not be set
                  rule: 'self.type == ''namespacedRef'' ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: 'self.type == ''konnectID'' ? has(self.konnectID) : true'
                - message: when type is konnectID, namespacedRef must not be set
                  rule: 'self.type == ''konnectID'' ? !has(self.namespacedRef) : true'
              type:
                description: Type is the type of the Konnect transit gateway.
                enum:
                - AWSTransitGateway
                - AzureTransitGateway
                type: string
            required:
            - networkRef
            - type
            type: object
            x-kubernetes-validations:
            - message: only namespacedRef is supported currently
              rule: self.networkRef.type == 'namespacedRef'
            - message: spec.type is immutable
              rule: self.type == oldSelf.type
            - message: must set spec.awsTransitGateway when spec.type is 'AWSTransitGateway'
              rule: 'self.type == ''AWSTransitGateway'' ? has(self.awsTransitGateway)
                : true'
            - message: must not set spec.awsTransitGateway when spec.type is not 'AWSTransitGateway'
              rule: 'self.type != ''AWSTransitGateway'' ? !has(self.awsTransitGateway)
                : true'
            - message: must set spec.azureTransitGateway when spec.type is 'AzureTransitGateway'
              rule: 'self.type == ''AzureTransitGateway'' ? has(self.azureTransitGateway)
                : true'
            - message: must not set spec.azureTransitGateway when spec.type is not
                'AzureTransitGateway'
              rule: 'self.type != ''AzureTransitGateway'' ? !has(self.azureTransitGateway)
                : true'
          status:
            description: Status defines the observed state of KonnectCloudGatewayTransitGateway.
            properties:
              conditions:
                default:
                - lastTransitionTime: "1970-01-01T00:00:00Z"
                  message: Waiting for controller
                  reason: Pending
                  status: Unknown
                  type: Programmed
                description: |-
                  Conditions describe the current conditions of the KonnectCloudGatewayDataPlaneGroupConfiguration.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              id:
                description: |-
                  ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                  If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                type: string
              networkID:
                description: NetworkID is the Konnect ID of the Konnect cloud gateway
                  network this entity is associated with.
                type: string
              organizationID:
                description: OrgID is ID of Konnect Org that this entity has been
                  created in.
                type: string
              serverURL:
                description: ServerURL is the URL of the Konnect server in which the
                  entity exists.
                type: string
              state:
                description: State is the state of the transit gateway on Konnect
                  side.
                type: string
            type: object
        required:
        - spec
        type: object
        x-kubernetes-validations:
        - message: spec.awsTransitGateway.name is immutable when transit gateway is
            already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : (!has(self.spec.awsTransitGateway)
            ? true : oldSelf.spec.awsTransitGateway.name == self.spec.awsTransitGateway.name)'
        - message: spec.azureTransitGateway.name is immutable when transit gateway
            is already Programmed
          rule: '(!has(self.status) || !self.status.conditions.exists(c, c.type ==
            ''Programmed'' && c.status == ''True'')) ? true : (!has(self.spec.azureTransitGateway)
            ? true : oldSelf.spec.azureTransitGateway.name == self.spec.azureTransitGateway.name)'
    served: true
    storage: true
    subresources:
      status: {}
