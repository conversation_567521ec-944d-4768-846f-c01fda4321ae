---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: ingress-controller,gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongconsumergroups.configuration.konghq.com
spec:
  group: configuration.konghq.com
  names:
    categories:
    - kong-ingress-controller
    - kong
    kind: KongConsumerGroup
    listKind: KongConsumerGroupList
    plural: kongconsumergroups
    shortNames:
    - kcg
    singular: kongconsumergroup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: Age
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    - jsonPath: .status.conditions[?(@.type=="Programmed")].status
      name: Programmed
      type: string
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: KongConsumerGroup is the Schema for the kongconsumergroups API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongConsumerGroupSpec defines the desired state of KongConsumerGroup.
            properties:
              controlPlaneRef:
                description: ControlPlaneRef is a reference to a ControlPlane this
                  ConsumerGroup is associated with.
                properties:
                  konnectID:
                    description: |-
                      KonnectID is the schema for the KonnectID type.
                      This field is required when the Type is konnectID.
                    pattern: ^[0-9a-f]{8}(?:\-[0-9a-f]{4}){3}-[0-9a-f]{12}$
                    type: string
                  konnectNamespacedRef:
                    description: |-
                      KonnectNamespacedRef is a reference to a Konnect Control Plane entity inside the cluster.
                      It contains the name of the Konnect Control Plane.
                      This field is required when the Type is konnectNamespacedRef.
                    properties:
                      name:
                        description: Name is the name of the Konnect Control Plane.
                        type: string
                      namespace:
                        description: |-
                          Namespace is the namespace where the Konnect Control Plane is in.
                          Currently only cluster scoped resources (KongVault) are allowed to set `konnectNamespacedRef.namespace`.
                        type: string
                    required:
                    - name
                    type: object
                  type:
                    default: kic
                    description: |-
                      Type indicates the type of the control plane being referenced. Allowed values:
                      - konnectID
                      - konnectNamespacedRef
                      - kic

                      The default is kic, which implies that the Control Plane is KIC.
                    enum:
                    - konnectID
                    - konnectNamespacedRef
                    - kic
                    type: string
                type: object
                x-kubernetes-validations:
                - message: '''konnectID'' type is not supported'
                  rule: self.type != 'konnectID'
                - message: when type is konnectNamespacedRef, konnectNamespacedRef
                    must be set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? has(self.konnectNamespacedRef) : true'
                - message: when type is konnectNamespacedRef, konnectID must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectNamespacedRef'')
                    ? !has(self.konnectID) : true'
                - message: when type is konnectID, konnectID must be set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? has(self.konnectID)
                    : true'
                - message: when type is konnectID, konnectNamespacedRef must not be
                    set
                  rule: '(has(self.type) && self.type == ''konnectID'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is kic, konnectID must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectID)
                    : true'
                - message: when type is kic, konnectNamespacedRef must not be set
                  rule: '(has(self.type) && self.type == ''kic'') ? !has(self.konnectNamespacedRef)
                    : true'
                - message: when type is unset, konnectID must not be set
                  rule: '!has(self.type) ? !has(self.konnectID) : true'
                - message: when type is unset, konnectNamespacedRef must not be set
                  rule: '!has(self.type) ? !has(self.konnectNamespacedRef) : true'
              name:
                description: Name is the name of the ConsumerGroup in Kong.
                type: string
              tags:
                description: Tags is an optional set of tags applied to the ConsumerGroup.
                items:
                  type: string
                maxItems: 20
                type: array
                x-kubernetes-validations:
                - message: tags entries must not be longer than 128 characters
                  rule: self.all(tag, size(tag) >= 1 && size(tag) <= 128)
            type: object
          status:
            default:
              conditions:
              - lastTransitionTime: "1970-01-01T00:00:00Z"
                message: Waiting for controller
                reason: Pending
                status: Unknown
                type: Programmed
            description: Status represents the current status of the KongConsumerGroup
              resource.
            properties:
              conditions:
                description: |-
                  Conditions describe the current conditions of the KongConsumerGroup.

                  Known condition types are:

                  * "Programmed"
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              konnect:
                description: Konnect contains the Konnect entity status.
                properties:
                  controlPlaneID:
                    description: ControlPlaneID is the Konnect ID of the ControlPlane
                      this Route is associated with.
                    type: string
                  id:
                    description: |-
                      ID is the unique identifier of the Konnect entity as assigned by Konnect API.
                      If it's unset (empty string), it means the Konnect entity hasn't been created yet.
                    type: string
                  organizationID:
                    description: OrgID is ID of Konnect Org that this entity has been
                      created in.
                    type: string
                  serverURL:
                    description: ServerURL is the URL of the Konnect server in which
                      the entity exists.
                    type: string
                type: object
            type: object
        type: object
        x-kubernetes-validations:
        - message: controlPlaneRef is required once set
          rule: (!has(oldSelf.spec) || !has(oldSelf.spec.controlPlaneRef)) || has(self.spec.controlPlaneRef)
        - message: spec.controlPlaneRef cannot specify namespace for namespaced resource
          rule: '(!has(self.spec) || !has(self.spec.controlPlaneRef) || !has(self.spec.controlPlaneRef.konnectNamespacedRef))
            ? true : !has(self.spec.controlPlaneRef.konnectNamespacedRef.__namespace__)'
        - message: spec.controlPlaneRef is immutable when an entity is already Programmed
          rule: '(!has(oldSelf.spec) || !has(oldSelf.spec.controlPlaneRef)) ? true
            : (!has(self.status) || !self.status.conditions.exists(c, c.type == ''Programmed''
            && c.status == ''True'')) ? true : oldSelf.spec.controlPlaneRef == self.spec.controlPlaneRef'
    served: true
    storage: true
    subresources:
      status: {}
