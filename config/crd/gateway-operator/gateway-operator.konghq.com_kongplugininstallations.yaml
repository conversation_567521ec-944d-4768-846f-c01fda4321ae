---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    kubernetes-configuration.konghq.com/channels: gateway-operator
    kubernetes-configuration.konghq.com/version: v1.4.0
  name: kongplugininstallations.gateway-operator.konghq.com
spec:
  group: gateway-operator.konghq.com
  names:
    categories:
    - kong
    - all
    kind: KongPluginInstallation
    listKind: KongPluginInstallationList
    plural: kongplugininstallations
    shortNames:
    - kpi
    singular: kongplugininstallation
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Resource is accepted
      jsonPath: .status.conditions[?(@.type=='Accepted')].status
      name: Accepted
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: |-
          KongPluginInstallation allows using a custom Kong Plugin distributed as a container image available in a registry.
          Such a plugin can be associated with GatewayConfiguration or DataPlane to be available for particular Kong Gateway
          and configured with KongPlugin CRD.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: KongPluginInstallationSpec provides the information necessary
              to retrieve and install a Kong custom plugin.
            properties:
              image:
                description: The image is an OCI image URL for a packaged custom Kong
                  plugin.
                type: string
              imagePullSecretRef:
                description: |-
                  ImagePullSecretRef is a reference to a Kubernetes Secret containing credentials necessary to pull the OCI image
                  in Image. It must follow the format in https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry.
                  It is optional. If the image is public, omit this field.
                properties:
                  group:
                    default: ""
                    description: |-
                      Group is the group of the referent. For example, "gateway.networking.k8s.io".
                      When unspecified or empty string, core API group is inferred.
                    maxLength: 253
                    pattern: ^$|^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$
                    type: string
                  kind:
                    default: Secret
                    description: Kind is kind of the referent. For example "Secret".
                    maxLength: 63
                    minLength: 1
                    pattern: ^[a-zA-Z]([-a-zA-Z0-9]*[a-zA-Z0-9])?$
                    type: string
                  name:
                    description: Name is the name of the referent.
                    maxLength: 253
                    minLength: 1
                    type: string
                  namespace:
                    description: |-
                      Namespace is the namespace of the referenced object. When unspecified, the local
                      namespace is inferred.

                      Note that when a namespace different than the local namespace is specified,
                      a ReferenceGrant object is required in the referent namespace to allow that
                      namespace's owner to accept the reference. See the ReferenceGrant
                      documentation for details.

                      Support: Core
                    maxLength: 63
                    minLength: 1
                    pattern: ^[a-z0-9]([-a-z0-9]*[a-z0-9])?$
                    type: string
                required:
                - name
                type: object
            required:
            - image
            type: object
          status:
            description: KongPluginInstallationStatus defines the observed state of
              KongPluginInstallation.
            properties:
              conditions:
                description: Conditions describe the current conditions of this KongPluginInstallation.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                maxItems: 8
                type: array
                x-kubernetes-list-map-keys:
                - type
                x-kubernetes-list-type: map
              underlyingConfigMapName:
                description: |-
                  UnderlyingConfigMapName is the name of the ConfigMap that contains the plugin's content.
                  It is set when the plugin is successfully fetched and unpacked.
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
