kind: KonnectGatewayControlPlane
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: group1
  namespace: default
spec:
  name: group1
  cluster_type: CLUSTER_TYPE_CONTROL_PLANE_GROUP
  labels:
    app: group1
    key1: group1
  konnect:
    authRef:
      name: konnect-api-auth-dev-1
---
kind: KonnectGatewayControlPlane
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: test1
  namespace: default
spec:
  name: test1
  labels:
    app: test1
    key1: test1
  konnect:
    authRef:
      name: konnect-api-auth-dev-1
---
kind: KonnectAPIAuthConfiguration
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: konnect-api-auth-dev-1
  namespace: default
spec:
  type: token
  token: kpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  serverURL: us.api.konghq.tech
