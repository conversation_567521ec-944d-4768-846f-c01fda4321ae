kind: KonnectAPIAuthConfiguration
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: konnect-api-auth-1
  namespace: default
spec:
  type: token
  token: kpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  serverURL: eu.api.konghq.com
---
kind: KonnectGatewayControlPlane
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: test1
  namespace: default
spec:
  name: test1
  labels:
    app: test1
    key1: test1
  konnect:
    authRef:
      name: konnect-api-auth-1
---
apiVersion: configuration.konghq.com/v1
kind: KongConsumer
metadata:
  name: consumer1
  namespace: default
username: consumer1
spec:
  controlPlaneRef:
    type: konnectNamespacedRef
    konnectNamespacedRef:
      name: test1
---
apiVersion: configuration.konghq.com/v1alpha1
kind: KongCredentialBasicAuth
metadata:
  name: basic-auth-1
  namespace: default
spec:
  consumerRef:
    name: consumer1
  password: pass
  username: username
