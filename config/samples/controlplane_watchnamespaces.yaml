apiVersion: gateway-operator.konghq.com/v1beta1
kind: ControlPlane
metadata:
  name: controlplane-example
spec:
  dataplane: dataplane-example
  gatewayClass: kong
  watchNamespaces:
    type: list
    list:
    - namespace-a
    - namespace-b
  deployment:
    podTemplateSpec:
      spec:
        containers:
        - name: controller
          # renovate: datasource=docker versioning=docker
          image: kong/kubernetes-ingress-controller:3.4.3
          readinessProbe:
            initialDelaySeconds: 1
            periodSeconds: 3
          resources:
            requests:
              memory: "64Mi"
              cpu: "250m"
            limits:
              memory: "1024Mi"
              cpu: "1000m"
