kind: KonnectAPIAuthConfiguration
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: konnect-api-auth
spec:
  type: token
  token: kpat_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
  serverURL: us.api.konghq.com
---
kind: KonnectGatewayControlPlane
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: cp-cloud-gateway-test1
  namespace: default
spec:
  cloud_gateway: true
  cluster_type: CLUSTER_TYPE_CONTROL_PLANE
  name: cp-konnectextension
  labels:
    app: test1
    key1: test1
  konnect:
    authRef:
      name: konnect-api-auth
---
apiVersion: konnect.konghq.com/v1alpha2
kind: KonnectExtension
metadata:
  name: my-konnect-extension
spec:
  konnect:
    controlPlane:
      ref:
        type: konnectNamespacedRef
        konnectNamespacedRef:
          name: cp-konnectextension
    dataPlane:
      labels:
        environment: "production"
        team: "platform"
        application: "payments"
  clientAuth:
    certificateSecret:
      provisioning: Automatic 
---
apiVersion: gateway-operator.konghq.com/v1beta1
kind: DataPlane
metadata:
  name: dataplane-konnect-extension-example
spec:
  extensions:
  - kind: KonnectExtension
    name: my-konnect-config
    group: gateway-operator.konghq.com
  deployment:
    replicas: 3
    podTemplateSpec:
      metadata:
        labels:
          dataplane-pod-label: example
        annotations:
          dataplane-pod-annotation: example
      spec:
        containers:
        - name: proxy
          # renovate: datasource=docker versioning=docker
          image: kong/kong-gateway:3.9
          readinessProbe:
            initialDelaySeconds: 1
            periodSeconds: 1
