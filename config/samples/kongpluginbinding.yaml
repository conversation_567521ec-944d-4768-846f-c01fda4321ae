apiVersion: configuration.konghq.com/v1alpha1
kind: KongPluginBinding
metadata:
  name: plugin-binding-kongservice-kongroute-kongconsumer
spec:
  pluginRef:
    name: plugin-sample
  controlPlaneRef:
    type: konnectNamespacedRef
    konnectNamespacedRef:
      name: test1
  targets:
    consumerRef:
      name: consumer-sample
    serviceRef:
      name: service-sample
      kind: KongService
      group: configuration.konghq.com
    routeRef:
      name: route-sample
      kind: KongRoute
      group: configuration.konghq.com
---
apiVersion: configuration.konghq.com/v1alpha1
kind: KongPluginBinding
metadata:
  name: plugin-binding-kongservice-kongconsumer
spec:
  controlPlaneRef:
    type: konnectNamespacedRef
    konnectNamespacedRef:
      name: test1
  pluginRef:
    name: plugin-sample
  targets:
    consumerRef:
      name: consumer-sample
    serviceRef:
      name: service-sample
      kind: KongService
      group: configuration.konghq.com
