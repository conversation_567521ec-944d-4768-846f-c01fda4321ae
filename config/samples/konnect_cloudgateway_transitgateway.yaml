kind: KonnectAPIAuthConfiguration
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: konnect-api-auth-1
spec:
  type: token
  token: kpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  serverURL: eu.api.konghq.com
---
apiVersion: konnect.konghq.com/v1alpha1
kind: KonnectCloudGatewayNetwork
metadata:
  name: network1
spec:
  name: network1
  cloud_gateway_provider_account_id: "1234"
  availability_zones:
  - us-west-1
  cidr_block: "********/24"
  region: us-west
  konnect:
    authRef:
      name: konnect-api-auth-1
---
apiVersion: konnect.konghq.com/v1alpha1
kind: KonnectCloudGatewayTransitGateway
metadata:
  name: aws-transit-gateway-1
spec:
  networkRef:
    type: namespacedRef
    namespacedRef:
      name: network1
  type: AWSTransitGateway
  awsTransitGateway:
    name: "aws-transit-gateway-us-west-1"
    cidr_blocks:
    - "*********/24"
    dns_config:
    - remote_dns_server_ip_addresses:
      - "*******"
      domain_proxy_list:
      - example.kong.com
    attachment_config:
      transit_gateway_id: "tgw-0123456789abcdef012"
      ram_share_arn: "arn:aws:ec2:us-west-1:************:transit-gateway/tgw-0123456789abcdef012"
