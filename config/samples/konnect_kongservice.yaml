kind: KonnectAPIAuthConfiguration
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: konnect-api-auth-1
  namespace: default
spec:
  type: token
  token: kpat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
  serverURL: eu.api.konghq.com
---
kind: KonnectGatewayControlPlane
apiVersion: konnect.konghq.com/v1alpha1
metadata:
  name: test1
  namespace: default
spec:
  name: test1
  labels:
    app: test1
    key1: test1
  konnect:
    authRef:
      name: konnect-api-auth-1
---
kind: KongService
apiVersion: configuration.konghq.com/v1alpha1
metadata:
  name: service-1
  namespace: default
spec:
  name: service-1
  host: example.com
  controlPlaneRef:
    type: konnectNamespacedRef
    konnectNamespacedRef:
      name: test1
