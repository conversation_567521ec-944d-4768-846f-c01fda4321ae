kind: GatewayConfiguration
apiVersion: gateway-operator.konghq.com/v1beta1
metadata:
  name: kong
  namespace: default
spec:
  dataPlaneOptions:
    resources:
      podDisruptionBudget:
        spec:
          maxUnavailable: 1
    deployment:
      replicas: 2
      podTemplateSpec:
        metadata:
          labels:
            dataplane-pod-label: example
          annotations:
            dataplane-pod-annotation: example
        spec:
          containers:
          - name: proxy
            # renovate: datasource=docker versioning=docker
            image: kong/kong-gateway:3.10
            readinessProbe:
              initialDelaySeconds: 1
              periodSeconds: 1
    network:
      services:
        ingress:
          annotations:
            foo: bar
  controlPlaneOptions:
    deployment:
      podTemplateSpec:
        spec:
          containers:
          - name: controller
            env:
            - name: CONTROLLER_LOG_LEVEL
              value: debug
