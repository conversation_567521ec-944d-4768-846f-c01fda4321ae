version: 2
updates:
- package-ecosystem: gomod
  directory: /
  schedule:
    interval: daily
  labels:
  - dependencies
  # Create a group of dependencies to be updated together in one pull request
  groups:
     # Specify a name for the group, which will be used in pull request titles and branch names
     k8s.io:
        # Define patterns to include dependencies in the group (based on dependency name)
        applies-to: version-updates # Applies the group rule to version updates
        patterns:
          - "k8s.io/*"
        exclude-patterns:
        - k8s.io/klog/*
        - k8s.io/utils
        - k8s.io/kube-openapi
- package-ecosystem: github-actions
  directory: /
  schedule:
    interval: daily
  labels:
  - github_actions
