name: Create release
run-name: "Release ${{ format('{0} (branch: {1})', inputs.tag, github.ref_name) }} "

on:
  workflow_call:
    inputs:
      tag:
        description: "Tag to release (e.g. v1.0.0)"
        required: true
        type: string
      latest:
        description: "Whether to tag this release latest"
        required: true
        type: boolean
  workflow_dispatch:
    inputs:
      tag:
        description: "Tag to release (e.g. v1.0.0)"
        required: true
        type: string
      latest:
        description: "Whether to tag this release latest"
        required: true
        type: boolean

permissions:
  contents: read

jobs:
  tests:
    name: Run tests
    uses: ./.github/workflows/tests.yaml

  verify-tag:
    name: Verify tag
    runs-on: ubuntu-latest
    outputs:
      # The prerelease part of the semver tag, if any. To be used in the release job.
      prerelease: ${{ steps.parse-semver.outputs.prerelease }}
    steps:
      - uses: mukunku/tag-exists-action@bdad1eaa119ce71b150b952c97351c75025c06a9 # v1.6.0
        id: check-tag
        with:
          tag: ${{ inputs.tag }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: fail if tag already exists
        if: ${{ steps.check-tag.outputs.exists == 'true' }}
        run: exit 1
      - name: Validate semver
        id: parse-semver
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ inputs.tag }}
          version_extractor_regex: 'v(.*)$'

  release:
    name: Create a GH release
    runs-on: ubuntu-latest
    needs:
      - tests
      - verify-tag
    permissions:
      contents: write # This is required to create GH releases.
    steps:
      - uses: ncipollo/release-action@440c8c1cb0ed28b9f43e4d1d670870f059653174 # v1.16.0
        with:
          body: |
            #### Changes

            See [changelog] for a complete list of changes.
            
            [changelog]: https://github.com/${{ github.repository }}/blob/main/CHANGELOG.md#${{ inputs.tag }}
            
            #### Install CRDs from all channels
            ```shell
            kustomize build github.com/${{ github.repository }}/config/crd/gateway-operator\?ref=${{ inputs.tag }} | kubectl apply -f -
            kustomize build github.com/${{ github.repository }}/config/crd/ingress-controller\?ref=${{ inputs.tag }} | kubectl apply -f -
            kustomize build github.com/${{ github.repository }}/config/crd/ingress-controller-incubator\?ref=${{ inputs.tag }} | kubectl apply -f -
            ```
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ inputs.tag }}
          commit: ${{ github.sha }}
          prerelease: ${{ needs.verify-tag.outputs.prerelease != '' }}
          makeLatest: ${{ inputs.latest == true }}
