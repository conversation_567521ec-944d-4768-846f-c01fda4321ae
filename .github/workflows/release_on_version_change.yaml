name: Trigger release based on VERSION file change

on:
  push:
    paths:
      - VERSION
    branches:
      - 'main'
      - 'release/*'

jobs:
  version:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      latest: ${{ steps.version.outputs.latest == 'latest' }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - id: version
        run: |
          echo "version=$(head -1 VERSION)" >> "$GITHUB_OUTPUT"
          echo "latest=$(tail -1 VERSION)" >> "$GITHUB_OUTPUT"
      - name: Validate semver
        uses: booxmedialtd/ws-action-parse-semver@7784200024d6b3fc01253e617ec0168daf603de3 # v1.4.7
        with:
          input_string: ${{ steps.version.outputs.version }}
          version_extractor_regex: 'v(.*)$'
  trigger_release:
    needs:
    - version
    uses: ./.github/workflows/release.yaml
    permissions:
      contents: write # This is required to create GH releases.
    with:
      tag: ${{ needs.version.outputs.version }}
      latest: ${{ needs.version.outputs.latest == 'true' }}
