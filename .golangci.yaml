version: "2"
run:
  timeout: 8m
linters:
  enable:
    - asciicheck
    - bodyclose
    - copyloopvar
    - dogsled
    - durationcheck
    - errorlint
    - exhaustive
    - forbidigo
    - gocritic
    - gomodguard
    - gosec
    - importas
    - misspell
    - nakedret
    - nilerr
    - nolintlint
    - predeclared
    - revive
    - unconvert
    - unparam
    - wastedassign
  settings:
    exhaustive:
      default-signifies-exhaustive: true
    gomodguard:
      blocked:
        modules:
          - golang.org/x/exp:
              recommendations:
                - maps
                - slices
                - github.com/samber/lo
          - github.com/pkg/errors:
              recommendations:
                - fmt
                - errors
          - github.com/sirupsen/logrus:
              recommendations:
                - sigs.k8s.io/controller-runtime/pkg/log
                - go.uber.org/zap/zapcore
    staticcheck:
      checks:
        - all
        # Incorrect or missing package comment.
        # https://staticcheck.dev/docs/checks/#ST1000
        - -ST1000
        # Incorrectly formatted error string.
        # https://staticcheck.dev/docs/checks/#ST1005
        - -ST1005
    importas:
      alias:
        - pkg: k8s.io/api/core/v1
          alias: corev1
        - pkg: k8s.io/api/apps/v1
          alias: appsv1
        - pkg: k8s.io/api/admission/v1
          alias: admissionv1
        - pkg: k8s.io/api/certificates/v1
          alias: certificatesv1
        - pkg: k8s.io/apimachinery/pkg/apis/meta/v1
          alias: metav1
        - pkg: sigs.k8s.io/gateway-api/apis/(v[\w\d]+)
          alias: gateway${1}
        - pkg: sigs.k8s.io/controller-runtime/pkg/log
          alias: ctrllog
        - pkg: github.com/Kong/sdk-konnect-go/models/components
          alias: sdkkonnectcomp
        - pkg: github.com/Kong/sdk-konnect-go/models/operations
          alias: sdkkonnectops
        - pkg: github.com/Kong/sdk-konnect-go/models/sdkerrors
          alias: sdkkonnecterrs
        - pkg: github.com/kong/kubernetes-configuration/api/common/(v[\w\d]+)
          alias: common${1}
        - pkg: github.com/kong/kubernetes-configuration/api/gateway-operator/(v[\w\d]+)
          alias: operator${1}
        - pkg: github.com/kong/kubernetes-configuration/api/configuration/(v[\w\d]+)
          alias: configuration${1}
        - pkg: github.com/kong/kubernetes-configuration/api/incubator/(v[\w\d]+)
          alias: incubator${1}
        - pkg: github.com/kong/kubernetes-configuration/api/konnect/(v[\w\d]+)
          alias: konnect${1}
      no-unaliased: true
    revive:
      rules:
        - name: errorf
          severity: warning
          disabled: false
        - name: error-strings
          severity: warning
          disabled: false
        - name: error-naming
          severity: warning
          disabled: false
        - name: duplicated-imports
          severity: warning
          disabled: false
        - name: empty-block
          severity: warning
          disabled: false
        - name: exported
          arguments:
            - checkPrivateReceivers
          severity: warning
          disabled: false
        - name: context-as-argument
          disabled: true
  exclusions:
    generated: lax
    presets:
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - zz_generated.+.go
      - pkg/clientset
      - third_party$
      - builtin$
      - examples$
issues:
  max-same-issues: 0
  fix: true
formatters:
  enable:
    - gci
    - gofmt
    - goimports
  settings:
    gci:
      sections:
        - standard
        - default
        - prefix(github.com/Kong/sdk-konnect-go)
        - prefix(github.com/kong/kubernetes-configuration)
  exclusions:
    generated: lax
    paths:
      - zz_generated.+.go
      - pkg/clientset
      - third_party$
      - builtin$
      - examples$
