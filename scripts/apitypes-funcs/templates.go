package main

const (
	konnectFuncOutputFileName             = "zz_generated_funcs.go"
	konnectFuncOutputCloudGatewayFilename = "zz_generated_funcs_cloud_gateway.go"
	konnectFuncOutputStandaloneFileName   = "zz_generated_funcs_standalone.go"
	konnectFuncTemplate                   = `package {{ .PackageVersion }}

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

{{- range .AdditionalImports }}
	{{ . }}
{{- end }}
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.

{{- range .Types }}
{{ if and (.KonnectStatusType) }}
{{ if (hasPrefix "*" .KonnectStatusType) }}
func (obj *{{ .Type }}) initKonnectStatus() {
	obj.Status.Konnect = &{{ trimPrefix "*" .KonnectStatusType }}{}
}
{{- end }}

// GetKonnectStatus returns the Konnect status contained in the {{ .Type }} status.
func (obj *{{ .Type }}) GetKonnectStatus() {{ .GetKonnectStatusReturnType }} {
{{- if .KonnectStatusType | hasPrefix "*" }}
	if obj.Status.Konnect == nil {
		return nil
	}
{{- end }}
{{- if .KonnectStatusEmbedded }}
	return &obj.Status.{{ .KonnectStatusType }}.KonnectEntityStatus
{{- else }}
	return &obj.Status.Konnect.KonnectEntityStatus
{{- end }}
}

// GetKonnectID returns the Konnect ID in the {{ .Type }} status.
func (obj *{{ .Type }}) GetKonnectID() string {
{{- if .KonnectStatusType | hasPrefix "*" }}
	if obj.Status.Konnect == nil {
		return ""
	}
{{- end }}
{{- if .KonnectStatusEmbedded }}
	return obj.Status.ID
{{- else }}
	return obj.Status.Konnect.KonnectEntityStatus.ID
{{- end }}
}

// SetKonnectID sets the Konnect ID in the {{ .Type }} status.
func (obj *{{ .Type }}) SetKonnectID(id string) {
{{- if .KonnectStatusType | hasPrefix "*" }}
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
{{- end }}
{{- if .KonnectStatusEmbedded }}
	obj.Status.ID = id
	{{- else }}
	obj.Status.Konnect.KonnectEntityStatus.ID = id
{{- end }}
}

// GetControlPlaneID returns the ControlPlane ID in the {{ .Type }} status.
func (obj *{{ .Type }}) GetControlPlaneID() string {
{{- if .KonnectStatusType | hasPrefix "*" }}
	if obj.Status.Konnect == nil {
		return ""
	}
{{- end }}
{{- if .KonnectStatusEmbedded }}
	return obj.Status.ControlPlaneID
{{- else }}
	return obj.Status.Konnect.ControlPlaneID
{{- end }}
}

// SetControlPlaneID sets the ControlPlane ID in the {{ .Type }} status.
func (obj *{{ .Type }}) SetControlPlaneID(id string) {
{{- if .KonnectStatusType | hasPrefix "*" }}
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
{{- end }}
{{- if .KonnectStatusEmbedded }}
	obj.Status.ControlPlaneID = id
{{- else }}
	obj.Status.Konnect.ControlPlaneID = id
{{- end }}
}
{{- end }}

// GetTypeName returns the {{ .Type }} Kind name
func (obj {{ .Type }}) GetTypeName() string {
	return "{{ .Type }}"
}

// GetConditions returns the Status Conditions
func (obj *{{ .Type }}) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *{{ .Type }}) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}
{{- if .ControlPlaneRefType }}

{{- $cpRefFieldPath := "Spec.ControlPlaneRef" }}
{{- if ne .ControlPlaneRefFieldPath "" }}
{{- $cpRefFieldPath = .ControlPlaneRefFieldPath }}
{{- end }}

// SetControlPlaneRef sets the ControlPlaneRef.
func (obj *{{ .Type }}) SetControlPlaneRef(ref *{{ .ControlPlaneRefType }}) {
	{{- if .ControlPlaneRefRequired }}
	if ref == nil {
		obj.{{ $cpRefFieldPath }} = {{ .ControlPlaneRefType }}{}
		return
	}
	{{- end }}
	obj.{{ $cpRefFieldPath }} = {{ if .ControlPlaneRefRequired }}*{{ end }}ref
}

// GetControlPlaneRef returns the ControlPlaneRef.
func (obj *{{ .Type }}) GetControlPlaneRef() *{{ .ControlPlaneRefType }} {
	return {{ if .ControlPlaneRefRequired }}&{{ end }}obj.{{ $cpRefFieldPath }}
}
{{- end }}

{{- if .ServiceRefType }}
{{- $serviceRefFieldPath := "Spec.ServiceRef" }}

// SetServiceRef sets the SetServiceRef.
func (obj *{{ .Type }}) SetServiceRef(ref *{{ .ServiceRefType }}) {
	{{- if .ControlPlaneRefRequired }}
	if ref == nil {
		obj.{{ $serviceRefFieldPath }} = {{ .ServiceRefType }}{}
		return
	}
	{{- end }}
	obj.{{ $serviceRefFieldPath }} = {{ if .ControlPlaneRefRequired }}*{{ end }}ref
}

// GetServiceRef returns the ServiceRef.
func (obj *{{ .Type }}) GetServiceRef() *{{ .ServiceRefType }} {
	return {{ if .ControlPlaneRefRequired }}&{{ end }}obj.{{ $serviceRefFieldPath }}
}
{{- end }}

{{- if hasSuffix "KonnectEntityStatusWithControlPlaneAndConsumerRefs" .KonnectStatusType }}

func (obj *{{ .Type }}) SetKonnectConsumerIDInStatus(id string) {
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
	obj.Status.Konnect.ConsumerID = id
}

func (obj *{{ .Type }}) GetConsumerRefName() string {
	return obj.Spec.ConsumerRef.Name
}
{{- end }}

{{- end }}
`
	konnectFuncStandaloneTemplate = `package {{ .PackageVersion }}

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.

{{- range .Types }}
{{ if .KonnectStatusType }}
// GetKonnectStatus returns the Konnect status contained in the {{ .Type }} status.
func (obj *{{ .Type }}) GetKonnectStatus() {{ .KonnectStatusType }} {
	return &obj.Status.{{ trimPrefix "*" .KonnectStatusType }}
}

// GetKonnectID returns the Konnect ID in the {{ .Type }} status.
func (obj *{{ .Type }}) GetKonnectID() string {
	return obj.Status.ID
}

// SetKonnectID sets the Konnect ID in the {{ .Type }} status.
func (obj *{{ .Type }}) SetKonnectID(id string) {
	obj.Status.ID = id
}

{{- end }}
// GetTypeName returns the {{ .Type }} Kind name
func (obj {{ .Type }}) GetTypeName() string {
	return "{{ .Type }}"
}

// GetConditions returns the Status Conditions
func (obj *{{ .Type }}) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *{{ .Type }}) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

{{- end }}
`

	konnectFuncNetworkRefTemplate = `package {{ .PackageVersion }}

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.

{{- range .Types }}
{{ if .KonnectStatusType }}

// GetKonnectStatus returns the Konnect status contained in the {{ .Type }} status.
func (obj *{{ .Type }}) GetKonnectStatus() {{ .GetKonnectStatusReturnType }} {
{{- if .KonnectStatusType | hasPrefix "*" }}
	if obj.Status.Konnect == nil {
		return nil
	}
{{- end }}
{{- if .KonnectStatusEmbedded }}
	return &obj.Status.{{ .KonnectStatusType }}.KonnectEntityStatus
{{- else }}
	return &obj.Status.Konnect.KonnectEntityStatus
{{- end }}
}
// GetKonnectID returns the Konnect ID in the {{ .Type }} status.
func (obj *{{ .Type }}) GetKonnectID() string {
	return obj.Status.ID
}

// SetKonnectID sets the Konnect ID in the {{ .Type }} status.
func (obj *{{ .Type }}) SetKonnectID(id string) {
	obj.Status.ID = id
}

{{- end }}
// GetTypeName returns the {{ .Type }} Kind name
func (obj {{ .Type }}) GetTypeName() string {
	return "{{ .Type }}"
}

// GetConditions returns the Status Conditions
func (obj *{{ .Type }}) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *{{ .Type }}) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}


// GetNetworkID returns Konnect network ID in the {{ .Type }} status.
func (obj *{{ .Type }}) GetNetworkID() string {
	return obj.Status.NetworkID
}

// SetNetworkID returns the Konnect network ID in the {{ .Type }} status.
func (obj *{{ .Type }}) SetNetworkID(id string) {
	obj.Status.NetworkID = id
}



{{- end }}
`

	listFuncOutputFileName = "zz_generated_list_funcs.go"
	listFuncTemplate       = `package {{ .PackageVersion }}

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.
{{- range .Types }}

// GetItems returns the list of {{ .Type }} items.
func (obj {{ .Type }}List) GetItems() []{{ .Type }} {
	return obj.Items
}
{{- end }}
`
)
