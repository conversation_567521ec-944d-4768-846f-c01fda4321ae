//go:build !ignore_autogenerated

/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceFacade) DeepCopyInto(out *KongServiceFacade) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceFacade.
func (in *KongServiceFacade) DeepCopy() *KongServiceFacade {
	if in == nil {
		return nil
	}
	out := new(KongServiceFacade)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongServiceFacade) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceFacadeBackend) DeepCopyInto(out *KongServiceFacadeBackend) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceFacadeBackend.
func (in *KongServiceFacadeBackend) DeepCopy() *KongServiceFacadeBackend {
	if in == nil {
		return nil
	}
	out := new(KongServiceFacadeBackend)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceFacadeList) DeepCopyInto(out *KongServiceFacadeList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongServiceFacade, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceFacadeList.
func (in *KongServiceFacadeList) DeepCopy() *KongServiceFacadeList {
	if in == nil {
		return nil
	}
	out := new(KongServiceFacadeList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongServiceFacadeList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceFacadeSpec) DeepCopyInto(out *KongServiceFacadeSpec) {
	*out = *in
	out.Backend = in.Backend
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceFacadeSpec.
func (in *KongServiceFacadeSpec) DeepCopy() *KongServiceFacadeSpec {
	if in == nil {
		return nil
	}
	out := new(KongServiceFacadeSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceFacadeStatus) DeepCopyInto(out *KongServiceFacadeStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceFacadeStatus.
func (in *KongServiceFacadeStatus) DeepCopy() *KongServiceFacadeStatus {
	if in == nil {
		return nil
	}
	out := new(KongServiceFacadeStatus)
	in.DeepCopyInto(out)
	return out
}
