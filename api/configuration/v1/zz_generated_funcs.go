package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.


func (obj *KongConsumer) initKonnectStatus() {
	obj.Status.Konnect = &konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef{}
}

// GetKonnectStatus returns the Konnect status contained in the KongConsumer status.
func (obj *KongConsumer) GetKonnectStatus() *konnectv1alpha1.KonnectEntityStatus {
	if obj.Status.Konnect == nil {
		return nil
	}
	return &obj.Status.Konnect.KonnectEntityStatus
}

// GetKonnectID returns the Konnect ID in the KongConsumer status.
func (obj *KongConsumer) GetKonnectID() string {
	if obj.Status.Konnect == nil {
		return ""
	}
	return obj.Status.Konnect.KonnectEntityStatus.ID
}

// SetKonnectID sets the Konnect ID in the KongConsumer status.
func (obj *KongConsumer) SetKonnectID(id string) {
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
	obj.Status.Konnect.KonnectEntityStatus.ID = id
}

// GetControlPlaneID returns the ControlPlane ID in the KongConsumer status.
func (obj *KongConsumer) GetControlPlaneID() string {
	if obj.Status.Konnect == nil {
		return ""
	}
	return obj.Status.Konnect.ControlPlaneID
}

// SetControlPlaneID sets the ControlPlane ID in the KongConsumer status.
func (obj *KongConsumer) SetControlPlaneID(id string) {
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
	obj.Status.Konnect.ControlPlaneID = id
}

// GetTypeName returns the KongConsumer Kind name
func (obj KongConsumer) GetTypeName() string {
	return "KongConsumer"
}

// GetConditions returns the Status Conditions
func (obj *KongConsumer) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KongConsumer) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

// SetControlPlaneRef sets the ControlPlaneRef.
func (obj *KongConsumer) SetControlPlaneRef(ref *commonv1alpha1.ControlPlaneRef) {
	obj.Spec.ControlPlaneRef = ref
}

// GetControlPlaneRef returns the ControlPlaneRef.
func (obj *KongConsumer) GetControlPlaneRef() *commonv1alpha1.ControlPlaneRef {
	return obj.Spec.ControlPlaneRef
}


// GetTypeName returns the KongPlugin Kind name
func (obj KongPlugin) GetTypeName() string {
	return "KongPlugin"
}

// GetConditions returns the Status Conditions
func (obj *KongPlugin) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KongPlugin) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}
