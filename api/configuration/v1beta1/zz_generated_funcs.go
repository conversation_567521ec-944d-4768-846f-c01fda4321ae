package v1beta1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.


func (obj *KongConsumerGroup) initKonnectStatus() {
	obj.Status.Konnect = &konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef{}
}

// GetKonnectStatus returns the Konnect status contained in the KongConsumerGroup status.
func (obj *KongConsumerGroup) GetKonnectStatus() *konnectv1alpha1.KonnectEntityStatus {
	if obj.Status.Konnect == nil {
		return nil
	}
	return &obj.Status.Konnect.KonnectEntityStatus
}

// GetKonnectID returns the Konnect ID in the KongConsumerGroup status.
func (obj *KongConsumerGroup) GetKonnectID() string {
	if obj.Status.Konnect == nil {
		return ""
	}
	return obj.Status.Konnect.KonnectEntityStatus.ID
}

// SetKonnectID sets the Konnect ID in the KongConsumerGroup status.
func (obj *KongConsumerGroup) SetKonnectID(id string) {
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
	obj.Status.Konnect.KonnectEntityStatus.ID = id
}

// GetControlPlaneID returns the ControlPlane ID in the KongConsumerGroup status.
func (obj *KongConsumerGroup) GetControlPlaneID() string {
	if obj.Status.Konnect == nil {
		return ""
	}
	return obj.Status.Konnect.ControlPlaneID
}

// SetControlPlaneID sets the ControlPlane ID in the KongConsumerGroup status.
func (obj *KongConsumerGroup) SetControlPlaneID(id string) {
	if obj.Status.Konnect == nil {
		obj.initKonnectStatus()
	}
	obj.Status.Konnect.ControlPlaneID = id
}

// GetTypeName returns the KongConsumerGroup Kind name
func (obj KongConsumerGroup) GetTypeName() string {
	return "KongConsumerGroup"
}

// GetConditions returns the Status Conditions
func (obj *KongConsumerGroup) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KongConsumerGroup) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

// SetControlPlaneRef sets the ControlPlaneRef.
func (obj *KongConsumerGroup) SetControlPlaneRef(ref *commonv1alpha1.ControlPlaneRef) {
	obj.Spec.ControlPlaneRef = ref
}

// GetControlPlaneRef returns the ControlPlaneRef.
func (obj *KongConsumerGroup) GetControlPlaneRef() *commonv1alpha1.ControlPlaneRef {
	return obj.Spec.ControlPlaneRef
}
