//go:build !ignore_autogenerated

/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	"github.com/Kong/sdk-konnect-go/models/components"
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneRef) DeepCopyInto(out *ControlPlaneRef) {
	*out = *in
	if in.KonnectID != nil {
		in, out := &in.KonnectID, &out.KonnectID
		*out = new(commonv1alpha1.KonnectIDType)
		**out = **in
	}
	if in.KonnectNamespacedRef != nil {
		in, out := &in.KonnectNamespacedRef, &out.KonnectNamespacedRef
		*out = new(commonv1alpha1.KonnectNamespacedRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneRef.
func (in *ControlPlaneRef) DeepCopy() *ControlPlaneRef {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControllerReference) DeepCopyInto(out *ControllerReference) {
	*out = *in
	if in.Group != nil {
		in, out := &in.Group, &out.Group
		*out = new(Group)
		**out = **in
	}
	if in.Kind != nil {
		in, out := &in.Kind, &out.Kind
		*out = new(Kind)
		**out = **in
	}
	if in.Namespace != nil {
		in, out := &in.Namespace, &out.Namespace
		*out = new(Namespace)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControllerReference.
func (in *ControllerReference) DeepCopy() *ControllerReference {
	if in == nil {
		return nil
	}
	out := new(ControllerReference)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IngressClassParameters) DeepCopyInto(out *IngressClassParameters) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IngressClassParameters.
func (in *IngressClassParameters) DeepCopy() *IngressClassParameters {
	if in == nil {
		return nil
	}
	out := new(IngressClassParameters)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *IngressClassParameters) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IngressClassParametersList) DeepCopyInto(out *IngressClassParametersList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]IngressClassParameters, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IngressClassParametersList.
func (in *IngressClassParametersList) DeepCopy() *IngressClassParametersList {
	if in == nil {
		return nil
	}
	out := new(IngressClassParametersList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *IngressClassParametersList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IngressClassParametersSpec) DeepCopyInto(out *IngressClassParametersSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IngressClassParametersSpec.
func (in *IngressClassParametersSpec) DeepCopy() *IngressClassParametersSpec {
	if in == nil {
		return nil
	}
	out := new(IngressClassParametersSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KeySetRef) DeepCopyInto(out *KeySetRef) {
	*out = *in
	if in.KonnectID != nil {
		in, out := &in.KonnectID, &out.KonnectID
		*out = new(string)
		**out = **in
	}
	if in.NamespacedRef != nil {
		in, out := &in.NamespacedRef, &out.NamespacedRef
		*out = new(commonv1alpha1.NameRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KeySetRef.
func (in *KeySetRef) DeepCopy() *KeySetRef {
	if in == nil {
		return nil
	}
	out := new(KeySetRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCACertificate) DeepCopyInto(out *KongCACertificate) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCACertificate.
func (in *KongCACertificate) DeepCopy() *KongCACertificate {
	if in == nil {
		return nil
	}
	out := new(KongCACertificate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCACertificate) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCACertificateAPISpec) DeepCopyInto(out *KongCACertificateAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCACertificateAPISpec.
func (in *KongCACertificateAPISpec) DeepCopy() *KongCACertificateAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCACertificateAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCACertificateList) DeepCopyInto(out *KongCACertificateList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCACertificate, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCACertificateList.
func (in *KongCACertificateList) DeepCopy() *KongCACertificateList {
	if in == nil {
		return nil
	}
	out := new(KongCACertificateList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCACertificateList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCACertificateSpec) DeepCopyInto(out *KongCACertificateSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongCACertificateAPISpec.DeepCopyInto(&out.KongCACertificateAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCACertificateSpec.
func (in *KongCACertificateSpec) DeepCopy() *KongCACertificateSpec {
	if in == nil {
		return nil
	}
	out := new(KongCACertificateSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCACertificateStatus) DeepCopyInto(out *KongCACertificateStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCACertificateStatus.
func (in *KongCACertificateStatus) DeepCopy() *KongCACertificateStatus {
	if in == nil {
		return nil
	}
	out := new(KongCACertificateStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCertificate) DeepCopyInto(out *KongCertificate) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCertificate.
func (in *KongCertificate) DeepCopy() *KongCertificate {
	if in == nil {
		return nil
	}
	out := new(KongCertificate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCertificate) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCertificateAPISpec) DeepCopyInto(out *KongCertificateAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCertificateAPISpec.
func (in *KongCertificateAPISpec) DeepCopy() *KongCertificateAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCertificateAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCertificateList) DeepCopyInto(out *KongCertificateList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCertificate, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCertificateList.
func (in *KongCertificateList) DeepCopy() *KongCertificateList {
	if in == nil {
		return nil
	}
	out := new(KongCertificateList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCertificateList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCertificateSpec) DeepCopyInto(out *KongCertificateSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongCertificateAPISpec.DeepCopyInto(&out.KongCertificateAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCertificateSpec.
func (in *KongCertificateSpec) DeepCopy() *KongCertificateSpec {
	if in == nil {
		return nil
	}
	out := new(KongCertificateSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCertificateStatus) DeepCopyInto(out *KongCertificateStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCertificateStatus.
func (in *KongCertificateStatus) DeepCopy() *KongCertificateStatus {
	if in == nil {
		return nil
	}
	out := new(KongCertificateStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialACL) DeepCopyInto(out *KongCredentialACL) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialACL.
func (in *KongCredentialACL) DeepCopy() *KongCredentialACL {
	if in == nil {
		return nil
	}
	out := new(KongCredentialACL)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialACL) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialACLAPISpec) DeepCopyInto(out *KongCredentialACLAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialACLAPISpec.
func (in *KongCredentialACLAPISpec) DeepCopy() *KongCredentialACLAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialACLAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialACLList) DeepCopyInto(out *KongCredentialACLList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCredentialACL, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialACLList.
func (in *KongCredentialACLList) DeepCopy() *KongCredentialACLList {
	if in == nil {
		return nil
	}
	out := new(KongCredentialACLList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialACLList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialACLSpec) DeepCopyInto(out *KongCredentialACLSpec) {
	*out = *in
	out.ConsumerRef = in.ConsumerRef
	in.KongCredentialACLAPISpec.DeepCopyInto(&out.KongCredentialACLAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialACLSpec.
func (in *KongCredentialACLSpec) DeepCopy() *KongCredentialACLSpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialACLSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialACLStatus) DeepCopyInto(out *KongCredentialACLStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndConsumerRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialACLStatus.
func (in *KongCredentialACLStatus) DeepCopy() *KongCredentialACLStatus {
	if in == nil {
		return nil
	}
	out := new(KongCredentialACLStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialAPIKey) DeepCopyInto(out *KongCredentialAPIKey) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialAPIKey.
func (in *KongCredentialAPIKey) DeepCopy() *KongCredentialAPIKey {
	if in == nil {
		return nil
	}
	out := new(KongCredentialAPIKey)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialAPIKey) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialAPIKeyAPISpec) DeepCopyInto(out *KongCredentialAPIKeyAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialAPIKeyAPISpec.
func (in *KongCredentialAPIKeyAPISpec) DeepCopy() *KongCredentialAPIKeyAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialAPIKeyAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialAPIKeyList) DeepCopyInto(out *KongCredentialAPIKeyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCredentialAPIKey, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialAPIKeyList.
func (in *KongCredentialAPIKeyList) DeepCopy() *KongCredentialAPIKeyList {
	if in == nil {
		return nil
	}
	out := new(KongCredentialAPIKeyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialAPIKeyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialAPIKeySpec) DeepCopyInto(out *KongCredentialAPIKeySpec) {
	*out = *in
	out.ConsumerRef = in.ConsumerRef
	in.KongCredentialAPIKeyAPISpec.DeepCopyInto(&out.KongCredentialAPIKeyAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialAPIKeySpec.
func (in *KongCredentialAPIKeySpec) DeepCopy() *KongCredentialAPIKeySpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialAPIKeySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialAPIKeyStatus) DeepCopyInto(out *KongCredentialAPIKeyStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndConsumerRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialAPIKeyStatus.
func (in *KongCredentialAPIKeyStatus) DeepCopy() *KongCredentialAPIKeyStatus {
	if in == nil {
		return nil
	}
	out := new(KongCredentialAPIKeyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialBasicAuth) DeepCopyInto(out *KongCredentialBasicAuth) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialBasicAuth.
func (in *KongCredentialBasicAuth) DeepCopy() *KongCredentialBasicAuth {
	if in == nil {
		return nil
	}
	out := new(KongCredentialBasicAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialBasicAuth) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialBasicAuthAPISpec) DeepCopyInto(out *KongCredentialBasicAuthAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialBasicAuthAPISpec.
func (in *KongCredentialBasicAuthAPISpec) DeepCopy() *KongCredentialBasicAuthAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialBasicAuthAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialBasicAuthList) DeepCopyInto(out *KongCredentialBasicAuthList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCredentialBasicAuth, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialBasicAuthList.
func (in *KongCredentialBasicAuthList) DeepCopy() *KongCredentialBasicAuthList {
	if in == nil {
		return nil
	}
	out := new(KongCredentialBasicAuthList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialBasicAuthList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialBasicAuthSpec) DeepCopyInto(out *KongCredentialBasicAuthSpec) {
	*out = *in
	out.ConsumerRef = in.ConsumerRef
	in.KongCredentialBasicAuthAPISpec.DeepCopyInto(&out.KongCredentialBasicAuthAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialBasicAuthSpec.
func (in *KongCredentialBasicAuthSpec) DeepCopy() *KongCredentialBasicAuthSpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialBasicAuthSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialBasicAuthStatus) DeepCopyInto(out *KongCredentialBasicAuthStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndConsumerRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialBasicAuthStatus.
func (in *KongCredentialBasicAuthStatus) DeepCopy() *KongCredentialBasicAuthStatus {
	if in == nil {
		return nil
	}
	out := new(KongCredentialBasicAuthStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialHMAC) DeepCopyInto(out *KongCredentialHMAC) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialHMAC.
func (in *KongCredentialHMAC) DeepCopy() *KongCredentialHMAC {
	if in == nil {
		return nil
	}
	out := new(KongCredentialHMAC)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialHMAC) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialHMACAPISpec) DeepCopyInto(out *KongCredentialHMACAPISpec) {
	*out = *in
	if in.ID != nil {
		in, out := &in.ID, &out.ID
		*out = new(string)
		**out = **in
	}
	if in.Secret != nil {
		in, out := &in.Secret, &out.Secret
		*out = new(string)
		**out = **in
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
	if in.Username != nil {
		in, out := &in.Username, &out.Username
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialHMACAPISpec.
func (in *KongCredentialHMACAPISpec) DeepCopy() *KongCredentialHMACAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialHMACAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialHMACList) DeepCopyInto(out *KongCredentialHMACList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCredentialHMAC, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialHMACList.
func (in *KongCredentialHMACList) DeepCopy() *KongCredentialHMACList {
	if in == nil {
		return nil
	}
	out := new(KongCredentialHMACList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialHMACList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialHMACSpec) DeepCopyInto(out *KongCredentialHMACSpec) {
	*out = *in
	out.ConsumerRef = in.ConsumerRef
	in.KongCredentialHMACAPISpec.DeepCopyInto(&out.KongCredentialHMACAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialHMACSpec.
func (in *KongCredentialHMACSpec) DeepCopy() *KongCredentialHMACSpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialHMACSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialHMACStatus) DeepCopyInto(out *KongCredentialHMACStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndConsumerRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialHMACStatus.
func (in *KongCredentialHMACStatus) DeepCopy() *KongCredentialHMACStatus {
	if in == nil {
		return nil
	}
	out := new(KongCredentialHMACStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialJWT) DeepCopyInto(out *KongCredentialJWT) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialJWT.
func (in *KongCredentialJWT) DeepCopy() *KongCredentialJWT {
	if in == nil {
		return nil
	}
	out := new(KongCredentialJWT)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialJWT) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialJWTAPISpec) DeepCopyInto(out *KongCredentialJWTAPISpec) {
	*out = *in
	if in.ID != nil {
		in, out := &in.ID, &out.ID
		*out = new(string)
		**out = **in
	}
	if in.Key != nil {
		in, out := &in.Key, &out.Key
		*out = new(string)
		**out = **in
	}
	if in.RSAPublicKey != nil {
		in, out := &in.RSAPublicKey, &out.RSAPublicKey
		*out = new(string)
		**out = **in
	}
	if in.Secret != nil {
		in, out := &in.Secret, &out.Secret
		*out = new(string)
		**out = **in
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialJWTAPISpec.
func (in *KongCredentialJWTAPISpec) DeepCopy() *KongCredentialJWTAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialJWTAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialJWTList) DeepCopyInto(out *KongCredentialJWTList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCredentialJWT, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialJWTList.
func (in *KongCredentialJWTList) DeepCopy() *KongCredentialJWTList {
	if in == nil {
		return nil
	}
	out := new(KongCredentialJWTList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCredentialJWTList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialJWTSpec) DeepCopyInto(out *KongCredentialJWTSpec) {
	*out = *in
	out.ConsumerRef = in.ConsumerRef
	in.KongCredentialJWTAPISpec.DeepCopyInto(&out.KongCredentialJWTAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialJWTSpec.
func (in *KongCredentialJWTSpec) DeepCopy() *KongCredentialJWTSpec {
	if in == nil {
		return nil
	}
	out := new(KongCredentialJWTSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCredentialJWTStatus) DeepCopyInto(out *KongCredentialJWTStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndConsumerRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCredentialJWTStatus.
func (in *KongCredentialJWTStatus) DeepCopy() *KongCredentialJWTStatus {
	if in == nil {
		return nil
	}
	out := new(KongCredentialJWTStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCustomEntity) DeepCopyInto(out *KongCustomEntity) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCustomEntity.
func (in *KongCustomEntity) DeepCopy() *KongCustomEntity {
	if in == nil {
		return nil
	}
	out := new(KongCustomEntity)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCustomEntity) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCustomEntityList) DeepCopyInto(out *KongCustomEntityList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongCustomEntity, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCustomEntityList.
func (in *KongCustomEntityList) DeepCopy() *KongCustomEntityList {
	if in == nil {
		return nil
	}
	out := new(KongCustomEntityList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongCustomEntityList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCustomEntitySpec) DeepCopyInto(out *KongCustomEntitySpec) {
	*out = *in
	in.Fields.DeepCopyInto(&out.Fields)
	if in.ParentRef != nil {
		in, out := &in.ParentRef, &out.ParentRef
		*out = new(ObjectReference)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCustomEntitySpec.
func (in *KongCustomEntitySpec) DeepCopy() *KongCustomEntitySpec {
	if in == nil {
		return nil
	}
	out := new(KongCustomEntitySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongCustomEntityStatus) DeepCopyInto(out *KongCustomEntityStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongCustomEntityStatus.
func (in *KongCustomEntityStatus) DeepCopy() *KongCustomEntityStatus {
	if in == nil {
		return nil
	}
	out := new(KongCustomEntityStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongDataPlaneClientCertificate) DeepCopyInto(out *KongDataPlaneClientCertificate) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongDataPlaneClientCertificate.
func (in *KongDataPlaneClientCertificate) DeepCopy() *KongDataPlaneClientCertificate {
	if in == nil {
		return nil
	}
	out := new(KongDataPlaneClientCertificate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongDataPlaneClientCertificate) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongDataPlaneClientCertificateAPISpec) DeepCopyInto(out *KongDataPlaneClientCertificateAPISpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongDataPlaneClientCertificateAPISpec.
func (in *KongDataPlaneClientCertificateAPISpec) DeepCopy() *KongDataPlaneClientCertificateAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongDataPlaneClientCertificateAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongDataPlaneClientCertificateList) DeepCopyInto(out *KongDataPlaneClientCertificateList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongDataPlaneClientCertificate, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongDataPlaneClientCertificateList.
func (in *KongDataPlaneClientCertificateList) DeepCopy() *KongDataPlaneClientCertificateList {
	if in == nil {
		return nil
	}
	out := new(KongDataPlaneClientCertificateList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongDataPlaneClientCertificateList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongDataPlaneClientCertificateSpec) DeepCopyInto(out *KongDataPlaneClientCertificateSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	out.KongDataPlaneClientCertificateAPISpec = in.KongDataPlaneClientCertificateAPISpec
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongDataPlaneClientCertificateSpec.
func (in *KongDataPlaneClientCertificateSpec) DeepCopy() *KongDataPlaneClientCertificateSpec {
	if in == nil {
		return nil
	}
	out := new(KongDataPlaneClientCertificateSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongDataPlaneClientCertificateStatus) DeepCopyInto(out *KongDataPlaneClientCertificateStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongDataPlaneClientCertificateStatus.
func (in *KongDataPlaneClientCertificateStatus) DeepCopy() *KongDataPlaneClientCertificateStatus {
	if in == nil {
		return nil
	}
	out := new(KongDataPlaneClientCertificateStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKey) DeepCopyInto(out *KongKey) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKey.
func (in *KongKey) DeepCopy() *KongKey {
	if in == nil {
		return nil
	}
	out := new(KongKey)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongKey) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeyAPISpec) DeepCopyInto(out *KongKeyAPISpec) {
	*out = *in
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.JWK != nil {
		in, out := &in.JWK, &out.JWK
		*out = new(string)
		**out = **in
	}
	if in.PEM != nil {
		in, out := &in.PEM, &out.PEM
		*out = new(PEMKeyPair)
		**out = **in
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeyAPISpec.
func (in *KongKeyAPISpec) DeepCopy() *KongKeyAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongKeyAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeyList) DeepCopyInto(out *KongKeyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongKey, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeyList.
func (in *KongKeyList) DeepCopy() *KongKeyList {
	if in == nil {
		return nil
	}
	out := new(KongKeyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongKeyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeySet) DeepCopyInto(out *KongKeySet) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeySet.
func (in *KongKeySet) DeepCopy() *KongKeySet {
	if in == nil {
		return nil
	}
	out := new(KongKeySet)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongKeySet) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeySetAPISpec) DeepCopyInto(out *KongKeySetAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeySetAPISpec.
func (in *KongKeySetAPISpec) DeepCopy() *KongKeySetAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongKeySetAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeySetList) DeepCopyInto(out *KongKeySetList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongKeySet, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeySetList.
func (in *KongKeySetList) DeepCopy() *KongKeySetList {
	if in == nil {
		return nil
	}
	out := new(KongKeySetList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongKeySetList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeySetSpec) DeepCopyInto(out *KongKeySetSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongKeySetAPISpec.DeepCopyInto(&out.KongKeySetAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeySetSpec.
func (in *KongKeySetSpec) DeepCopy() *KongKeySetSpec {
	if in == nil {
		return nil
	}
	out := new(KongKeySetSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeySetStatus) DeepCopyInto(out *KongKeySetStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeySetStatus.
func (in *KongKeySetStatus) DeepCopy() *KongKeySetStatus {
	if in == nil {
		return nil
	}
	out := new(KongKeySetStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeySpec) DeepCopyInto(out *KongKeySpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	if in.KeySetRef != nil {
		in, out := &in.KeySetRef, &out.KeySetRef
		*out = new(KeySetRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongKeyAPISpec.DeepCopyInto(&out.KongKeyAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeySpec.
func (in *KongKeySpec) DeepCopy() *KongKeySpec {
	if in == nil {
		return nil
	}
	out := new(KongKeySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongKeyStatus) DeepCopyInto(out *KongKeyStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndKeySetRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongKeyStatus.
func (in *KongKeyStatus) DeepCopy() *KongKeyStatus {
	if in == nil {
		return nil
	}
	out := new(KongKeyStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongLicense) DeepCopyInto(out *KongLicense) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongLicense.
func (in *KongLicense) DeepCopy() *KongLicense {
	if in == nil {
		return nil
	}
	out := new(KongLicense)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongLicense) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongLicenseControllerStatus) DeepCopyInto(out *KongLicenseControllerStatus) {
	*out = *in
	if in.ControllerRef != nil {
		in, out := &in.ControllerRef, &out.ControllerRef
		*out = new(ControllerReference)
		(*in).DeepCopyInto(*out)
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongLicenseControllerStatus.
func (in *KongLicenseControllerStatus) DeepCopy() *KongLicenseControllerStatus {
	if in == nil {
		return nil
	}
	out := new(KongLicenseControllerStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongLicenseList) DeepCopyInto(out *KongLicenseList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongLicense, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongLicenseList.
func (in *KongLicenseList) DeepCopy() *KongLicenseList {
	if in == nil {
		return nil
	}
	out := new(KongLicenseList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongLicenseList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongLicenseStatus) DeepCopyInto(out *KongLicenseStatus) {
	*out = *in
	if in.KongLicenseControllerStatuses != nil {
		in, out := &in.KongLicenseControllerStatuses, &out.KongLicenseControllerStatuses
		*out = make([]KongLicenseControllerStatus, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongLicenseStatus.
func (in *KongLicenseStatus) DeepCopy() *KongLicenseStatus {
	if in == nil {
		return nil
	}
	out := new(KongLicenseStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginBinding) DeepCopyInto(out *KongPluginBinding) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginBinding.
func (in *KongPluginBinding) DeepCopy() *KongPluginBinding {
	if in == nil {
		return nil
	}
	out := new(KongPluginBinding)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongPluginBinding) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginBindingList) DeepCopyInto(out *KongPluginBindingList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongPluginBinding, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginBindingList.
func (in *KongPluginBindingList) DeepCopy() *KongPluginBindingList {
	if in == nil {
		return nil
	}
	out := new(KongPluginBindingList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongPluginBindingList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginBindingSpec) DeepCopyInto(out *KongPluginBindingSpec) {
	*out = *in
	in.PluginReference.DeepCopyInto(&out.PluginReference)
	if in.Targets != nil {
		in, out := &in.Targets, &out.Targets
		*out = new(KongPluginBindingTargets)
		(*in).DeepCopyInto(*out)
	}
	in.ControlPlaneRef.DeepCopyInto(&out.ControlPlaneRef)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginBindingSpec.
func (in *KongPluginBindingSpec) DeepCopy() *KongPluginBindingSpec {
	if in == nil {
		return nil
	}
	out := new(KongPluginBindingSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginBindingStatus) DeepCopyInto(out *KongPluginBindingStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginBindingStatus.
func (in *KongPluginBindingStatus) DeepCopy() *KongPluginBindingStatus {
	if in == nil {
		return nil
	}
	out := new(KongPluginBindingStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongPluginBindingTargets) DeepCopyInto(out *KongPluginBindingTargets) {
	*out = *in
	if in.RouteReference != nil {
		in, out := &in.RouteReference, &out.RouteReference
		*out = new(TargetRefWithGroupKind)
		**out = **in
	}
	if in.ServiceReference != nil {
		in, out := &in.ServiceReference, &out.ServiceReference
		*out = new(TargetRefWithGroupKind)
		**out = **in
	}
	if in.ConsumerReference != nil {
		in, out := &in.ConsumerReference, &out.ConsumerReference
		*out = new(TargetRef)
		**out = **in
	}
	if in.ConsumerGroupReference != nil {
		in, out := &in.ConsumerGroupReference, &out.ConsumerGroupReference
		*out = new(TargetRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongPluginBindingTargets.
func (in *KongPluginBindingTargets) DeepCopy() *KongPluginBindingTargets {
	if in == nil {
		return nil
	}
	out := new(KongPluginBindingTargets)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongRoute) DeepCopyInto(out *KongRoute) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongRoute.
func (in *KongRoute) DeepCopy() *KongRoute {
	if in == nil {
		return nil
	}
	out := new(KongRoute)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongRoute) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongRouteAPISpec) DeepCopyInto(out *KongRouteAPISpec) {
	*out = *in
	if in.Destinations != nil {
		in, out := &in.Destinations, &out.Destinations
		*out = make([]components.Destinations, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Headers != nil {
		in, out := &in.Headers, &out.Headers
		*out = make(map[string][]string, len(*in))
		for key, val := range *in {
			var outVal []string
			if val == nil {
				(*out)[key] = nil
			} else {
				inVal := (*in)[key]
				in, out := &inVal, &outVal
				*out = make([]string, len(*in))
				copy(*out, *in)
			}
			(*out)[key] = outVal
		}
	}
	if in.Hosts != nil {
		in, out := &in.Hosts, &out.Hosts
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.HTTPSRedirectStatusCode != nil {
		in, out := &in.HTTPSRedirectStatusCode, &out.HTTPSRedirectStatusCode
		*out = new(components.HTTPSRedirectStatusCode)
		**out = **in
	}
	if in.Methods != nil {
		in, out := &in.Methods, &out.Methods
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.PathHandling != nil {
		in, out := &in.PathHandling, &out.PathHandling
		*out = new(components.PathHandling)
		**out = **in
	}
	if in.Paths != nil {
		in, out := &in.Paths, &out.Paths
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.PreserveHost != nil {
		in, out := &in.PreserveHost, &out.PreserveHost
		*out = new(bool)
		**out = **in
	}
	if in.Protocols != nil {
		in, out := &in.Protocols, &out.Protocols
		*out = make([]components.RouteJSONProtocols, len(*in))
		copy(*out, *in)
	}
	if in.RegexPriority != nil {
		in, out := &in.RegexPriority, &out.RegexPriority
		*out = new(int64)
		**out = **in
	}
	if in.RequestBuffering != nil {
		in, out := &in.RequestBuffering, &out.RequestBuffering
		*out = new(bool)
		**out = **in
	}
	if in.ResponseBuffering != nil {
		in, out := &in.ResponseBuffering, &out.ResponseBuffering
		*out = new(bool)
		**out = **in
	}
	if in.Snis != nil {
		in, out := &in.Snis, &out.Snis
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Sources != nil {
		in, out := &in.Sources, &out.Sources
		*out = make([]components.Sources, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.StripPath != nil {
		in, out := &in.StripPath, &out.StripPath
		*out = new(bool)
		**out = **in
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongRouteAPISpec.
func (in *KongRouteAPISpec) DeepCopy() *KongRouteAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongRouteAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongRouteList) DeepCopyInto(out *KongRouteList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongRoute, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongRouteList.
func (in *KongRouteList) DeepCopy() *KongRouteList {
	if in == nil {
		return nil
	}
	out := new(KongRouteList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongRouteList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongRouteSpec) DeepCopyInto(out *KongRouteSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	if in.ServiceRef != nil {
		in, out := &in.ServiceRef, &out.ServiceRef
		*out = new(ServiceRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongRouteAPISpec.DeepCopyInto(&out.KongRouteAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongRouteSpec.
func (in *KongRouteSpec) DeepCopy() *KongRouteSpec {
	if in == nil {
		return nil
	}
	out := new(KongRouteSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongRouteStatus) DeepCopyInto(out *KongRouteStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndServiceRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongRouteStatus.
func (in *KongRouteStatus) DeepCopy() *KongRouteStatus {
	if in == nil {
		return nil
	}
	out := new(KongRouteStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongSNI) DeepCopyInto(out *KongSNI) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongSNI.
func (in *KongSNI) DeepCopy() *KongSNI {
	if in == nil {
		return nil
	}
	out := new(KongSNI)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongSNI) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongSNIAPISpec) DeepCopyInto(out *KongSNIAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongSNIAPISpec.
func (in *KongSNIAPISpec) DeepCopy() *KongSNIAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongSNIAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongSNIList) DeepCopyInto(out *KongSNIList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongSNI, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongSNIList.
func (in *KongSNIList) DeepCopy() *KongSNIList {
	if in == nil {
		return nil
	}
	out := new(KongSNIList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongSNIList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongSNISpec) DeepCopyInto(out *KongSNISpec) {
	*out = *in
	out.CertificateRef = in.CertificateRef
	in.KongSNIAPISpec.DeepCopyInto(&out.KongSNIAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongSNISpec.
func (in *KongSNISpec) DeepCopy() *KongSNISpec {
	if in == nil {
		return nil
	}
	out := new(KongSNISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongSNIStatus) DeepCopyInto(out *KongSNIStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndCertificateRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongSNIStatus.
func (in *KongSNIStatus) DeepCopy() *KongSNIStatus {
	if in == nil {
		return nil
	}
	out := new(KongSNIStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongService) DeepCopyInto(out *KongService) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongService.
func (in *KongService) DeepCopy() *KongService {
	if in == nil {
		return nil
	}
	out := new(KongService)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongService) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceAPISpec) DeepCopyInto(out *KongServiceAPISpec) {
	*out = *in
	if in.URL != nil {
		in, out := &in.URL, &out.URL
		*out = new(string)
		**out = **in
	}
	if in.ConnectTimeout != nil {
		in, out := &in.ConnectTimeout, &out.ConnectTimeout
		*out = new(int64)
		**out = **in
	}
	if in.Enabled != nil {
		in, out := &in.Enabled, &out.Enabled
		*out = new(bool)
		**out = **in
	}
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.Path != nil {
		in, out := &in.Path, &out.Path
		*out = new(string)
		**out = **in
	}
	if in.ReadTimeout != nil {
		in, out := &in.ReadTimeout, &out.ReadTimeout
		*out = new(int64)
		**out = **in
	}
	if in.Retries != nil {
		in, out := &in.Retries, &out.Retries
		*out = new(int64)
		**out = **in
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
	if in.TLSVerify != nil {
		in, out := &in.TLSVerify, &out.TLSVerify
		*out = new(bool)
		**out = **in
	}
	if in.TLSVerifyDepth != nil {
		in, out := &in.TLSVerifyDepth, &out.TLSVerifyDepth
		*out = new(int64)
		**out = **in
	}
	if in.WriteTimeout != nil {
		in, out := &in.WriteTimeout, &out.WriteTimeout
		*out = new(int64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceAPISpec.
func (in *KongServiceAPISpec) DeepCopy() *KongServiceAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongServiceAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceList) DeepCopyInto(out *KongServiceList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongService, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceList.
func (in *KongServiceList) DeepCopy() *KongServiceList {
	if in == nil {
		return nil
	}
	out := new(KongServiceList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongServiceList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceSpec) DeepCopyInto(out *KongServiceSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongServiceAPISpec.DeepCopyInto(&out.KongServiceAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceSpec.
func (in *KongServiceSpec) DeepCopy() *KongServiceSpec {
	if in == nil {
		return nil
	}
	out := new(KongServiceSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongServiceStatus) DeepCopyInto(out *KongServiceStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongServiceStatus.
func (in *KongServiceStatus) DeepCopy() *KongServiceStatus {
	if in == nil {
		return nil
	}
	out := new(KongServiceStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongTarget) DeepCopyInto(out *KongTarget) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongTarget.
func (in *KongTarget) DeepCopy() *KongTarget {
	if in == nil {
		return nil
	}
	out := new(KongTarget)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongTarget) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongTargetAPISpec) DeepCopyInto(out *KongTargetAPISpec) {
	*out = *in
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongTargetAPISpec.
func (in *KongTargetAPISpec) DeepCopy() *KongTargetAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongTargetAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongTargetList) DeepCopyInto(out *KongTargetList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongTarget, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongTargetList.
func (in *KongTargetList) DeepCopy() *KongTargetList {
	if in == nil {
		return nil
	}
	out := new(KongTargetList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongTargetList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongTargetSpec) DeepCopyInto(out *KongTargetSpec) {
	*out = *in
	out.UpstreamRef = in.UpstreamRef
	in.KongTargetAPISpec.DeepCopyInto(&out.KongTargetAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongTargetSpec.
func (in *KongTargetSpec) DeepCopy() *KongTargetSpec {
	if in == nil {
		return nil
	}
	out := new(KongTargetSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongTargetStatus) DeepCopyInto(out *KongTargetStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneAndUpstreamRefs)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongTargetStatus.
func (in *KongTargetStatus) DeepCopy() *KongTargetStatus {
	if in == nil {
		return nil
	}
	out := new(KongTargetStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongUpstream) DeepCopyInto(out *KongUpstream) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongUpstream.
func (in *KongUpstream) DeepCopy() *KongUpstream {
	if in == nil {
		return nil
	}
	out := new(KongUpstream)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongUpstream) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongUpstreamAPISpec) DeepCopyInto(out *KongUpstreamAPISpec) {
	*out = *in
	if in.Algorithm != nil {
		in, out := &in.Algorithm, &out.Algorithm
		*out = new(components.UpstreamAlgorithm)
		**out = **in
	}
	if in.ClientCertificate != nil {
		in, out := &in.ClientCertificate, &out.ClientCertificate
		*out = new(components.UpstreamClientCertificate)
		(*in).DeepCopyInto(*out)
	}
	if in.HashFallback != nil {
		in, out := &in.HashFallback, &out.HashFallback
		*out = new(components.HashFallback)
		**out = **in
	}
	if in.HashFallbackHeader != nil {
		in, out := &in.HashFallbackHeader, &out.HashFallbackHeader
		*out = new(string)
		**out = **in
	}
	if in.HashFallbackQueryArg != nil {
		in, out := &in.HashFallbackQueryArg, &out.HashFallbackQueryArg
		*out = new(string)
		**out = **in
	}
	if in.HashFallbackURICapture != nil {
		in, out := &in.HashFallbackURICapture, &out.HashFallbackURICapture
		*out = new(string)
		**out = **in
	}
	if in.HashOn != nil {
		in, out := &in.HashOn, &out.HashOn
		*out = new(components.HashOn)
		**out = **in
	}
	if in.HashOnCookie != nil {
		in, out := &in.HashOnCookie, &out.HashOnCookie
		*out = new(string)
		**out = **in
	}
	if in.HashOnCookiePath != nil {
		in, out := &in.HashOnCookiePath, &out.HashOnCookiePath
		*out = new(string)
		**out = **in
	}
	if in.HashOnHeader != nil {
		in, out := &in.HashOnHeader, &out.HashOnHeader
		*out = new(string)
		**out = **in
	}
	if in.HashOnQueryArg != nil {
		in, out := &in.HashOnQueryArg, &out.HashOnQueryArg
		*out = new(string)
		**out = **in
	}
	if in.HashOnURICapture != nil {
		in, out := &in.HashOnURICapture, &out.HashOnURICapture
		*out = new(string)
		**out = **in
	}
	if in.Healthchecks != nil {
		in, out := &in.Healthchecks, &out.Healthchecks
		*out = new(components.Healthchecks)
		(*in).DeepCopyInto(*out)
	}
	if in.HostHeader != nil {
		in, out := &in.HostHeader, &out.HostHeader
		*out = new(string)
		**out = **in
	}
	if in.Slots != nil {
		in, out := &in.Slots, &out.Slots
		*out = new(int64)
		**out = **in
	}
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
	if in.UseSrvName != nil {
		in, out := &in.UseSrvName, &out.UseSrvName
		*out = new(bool)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongUpstreamAPISpec.
func (in *KongUpstreamAPISpec) DeepCopy() *KongUpstreamAPISpec {
	if in == nil {
		return nil
	}
	out := new(KongUpstreamAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongUpstreamList) DeepCopyInto(out *KongUpstreamList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongUpstream, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongUpstreamList.
func (in *KongUpstreamList) DeepCopy() *KongUpstreamList {
	if in == nil {
		return nil
	}
	out := new(KongUpstreamList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongUpstreamList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongUpstreamSpec) DeepCopyInto(out *KongUpstreamSpec) {
	*out = *in
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
	in.KongUpstreamAPISpec.DeepCopyInto(&out.KongUpstreamAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongUpstreamSpec.
func (in *KongUpstreamSpec) DeepCopy() *KongUpstreamSpec {
	if in == nil {
		return nil
	}
	out := new(KongUpstreamSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongUpstreamStatus) DeepCopyInto(out *KongUpstreamStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongUpstreamStatus.
func (in *KongUpstreamStatus) DeepCopy() *KongUpstreamStatus {
	if in == nil {
		return nil
	}
	out := new(KongUpstreamStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongVault) DeepCopyInto(out *KongVault) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongVault.
func (in *KongVault) DeepCopy() *KongVault {
	if in == nil {
		return nil
	}
	out := new(KongVault)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongVault) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongVaultList) DeepCopyInto(out *KongVaultList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KongVault, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongVaultList.
func (in *KongVaultList) DeepCopy() *KongVaultList {
	if in == nil {
		return nil
	}
	out := new(KongVaultList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KongVaultList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongVaultSpec) DeepCopyInto(out *KongVaultSpec) {
	*out = *in
	in.Config.DeepCopyInto(&out.Config)
	if in.Tags != nil {
		in, out := &in.Tags, &out.Tags
		*out = make(commonv1alpha1.Tags, len(*in))
		copy(*out, *in)
	}
	if in.ControlPlaneRef != nil {
		in, out := &in.ControlPlaneRef, &out.ControlPlaneRef
		*out = new(commonv1alpha1.ControlPlaneRef)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongVaultSpec.
func (in *KongVaultSpec) DeepCopy() *KongVaultSpec {
	if in == nil {
		return nil
	}
	out := new(KongVaultSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KongVaultStatus) DeepCopyInto(out *KongVaultStatus) {
	*out = *in
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(konnectv1alpha1.KonnectEntityStatusWithControlPlaneRef)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KongVaultStatus.
func (in *KongVaultStatus) DeepCopy() *KongVaultStatus {
	if in == nil {
		return nil
	}
	out := new(KongVaultStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ObjectReference) DeepCopyInto(out *ObjectReference) {
	*out = *in
	if in.Group != nil {
		in, out := &in.Group, &out.Group
		*out = new(string)
		**out = **in
	}
	if in.Kind != nil {
		in, out := &in.Kind, &out.Kind
		*out = new(string)
		**out = **in
	}
	if in.Namespace != nil {
		in, out := &in.Namespace, &out.Namespace
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ObjectReference.
func (in *ObjectReference) DeepCopy() *ObjectReference {
	if in == nil {
		return nil
	}
	out := new(ObjectReference)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PEMKeyPair) DeepCopyInto(out *PEMKeyPair) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PEMKeyPair.
func (in *PEMKeyPair) DeepCopy() *PEMKeyPair {
	if in == nil {
		return nil
	}
	out := new(PEMKeyPair)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PluginRef) DeepCopyInto(out *PluginRef) {
	*out = *in
	if in.Kind != nil {
		in, out := &in.Kind, &out.Kind
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PluginRef.
func (in *PluginRef) DeepCopy() *PluginRef {
	if in == nil {
		return nil
	}
	out := new(PluginRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ServiceRef) DeepCopyInto(out *ServiceRef) {
	*out = *in
	if in.NamespacedRef != nil {
		in, out := &in.NamespacedRef, &out.NamespacedRef
		*out = new(commonv1alpha1.NameRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ServiceRef.
func (in *ServiceRef) DeepCopy() *ServiceRef {
	if in == nil {
		return nil
	}
	out := new(ServiceRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TargetRef) DeepCopyInto(out *TargetRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TargetRef.
func (in *TargetRef) DeepCopy() *TargetRef {
	if in == nil {
		return nil
	}
	out := new(TargetRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TargetRefWithGroupKind) DeepCopyInto(out *TargetRefWithGroupKind) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TargetRefWithGroupKind.
func (in *TargetRefWithGroupKind) DeepCopy() *TargetRefWithGroupKind {
	if in == nil {
		return nil
	}
	out := new(TargetRefWithGroupKind)
	in.DeepCopyInto(out)
	return out
}
