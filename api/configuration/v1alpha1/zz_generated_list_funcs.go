package v1alpha1

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.

// GetItems returns the list of KongKey items.
func (obj KongKeyList) GetItems() []KongKey {
	return obj.Items
}

// GetItems returns the list of KongKeySet items.
func (obj KongKeySetList) GetItems() []KongKeySet {
	return obj.Items
}

// GetItems returns the list of KongCredentialBasicAuth items.
func (obj KongCredentialBasicAuthList) GetItems() []KongCredentialBasicAuth {
	return obj.Items
}

// GetItems returns the list of KongCredentialAPIKey items.
func (obj KongCredentialAPIKeyList) GetItems() []KongCredentialAPIKey {
	return obj.Items
}

// GetItems returns the list of KongCredentialJWT items.
func (obj KongCredentialJWTList) GetItems() []KongCredentialJWT {
	return obj.Items
}

// GetItems returns the list of KongCredentialACL items.
func (obj KongCredentialACLList) GetItems() []KongCredentialACL {
	return obj.Items
}

// GetItems returns the list of KongCredentialHMAC items.
func (obj KongCredentialHMACList) GetItems() []KongCredentialHMAC {
	return obj.Items
}

// GetItems returns the list of KongCACertificate items.
func (obj KongCACertificateList) GetItems() []KongCACertificate {
	return obj.Items
}

// GetItems returns the list of KongCertificate items.
func (obj KongCertificateList) GetItems() []KongCertificate {
	return obj.Items
}

// GetItems returns the list of KongPluginBinding items.
func (obj KongPluginBindingList) GetItems() []KongPluginBinding {
	return obj.Items
}

// GetItems returns the list of KongService items.
func (obj KongServiceList) GetItems() []KongService {
	return obj.Items
}

// GetItems returns the list of KongRoute items.
func (obj KongRouteList) GetItems() []KongRoute {
	return obj.Items
}

// GetItems returns the list of KongUpstream items.
func (obj KongUpstreamList) GetItems() []KongUpstream {
	return obj.Items
}

// GetItems returns the list of KongTarget items.
func (obj KongTargetList) GetItems() []KongTarget {
	return obj.Items
}

// GetItems returns the list of KongVault items.
func (obj KongVaultList) GetItems() []KongVault {
	return obj.Items
}

// GetItems returns the list of KongSNI items.
func (obj KongSNIList) GetItems() []KongSNI {
	return obj.Items
}

// GetItems returns the list of KongDataPlaneClientCertificate items.
func (obj KongDataPlaneClientCertificateList) GetItems() []KongDataPlaneClientCertificate {
	return obj.Items
}
