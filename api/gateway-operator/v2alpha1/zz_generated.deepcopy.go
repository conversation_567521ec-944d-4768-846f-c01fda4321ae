//go:build !ignore_autogenerated

/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v2alpha1

import (
	"github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	"github.com/kong/kubernetes-configuration/api/gateway-operator/v1beta1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlane) DeepCopyInto(out *ControlPlane) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlane.
func (in *ControlPlane) DeepCopy() *ControlPlane {
	if in == nil {
		return nil
	}
	out := new(ControlPlane)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ControlPlane) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneAdminAPI) DeepCopyInto(out *ControlPlaneAdminAPI) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneAdminAPI.
func (in *ControlPlaneAdminAPI) DeepCopy() *ControlPlaneAdminAPI {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneAdminAPI)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneController) DeepCopyInto(out *ControlPlaneController) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneController.
func (in *ControlPlaneController) DeepCopy() *ControlPlaneController {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneController)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneDataPlaneTarget) DeepCopyInto(out *ControlPlaneDataPlaneTarget) {
	*out = *in
	if in.External != nil {
		in, out := &in.External, &out.External
		*out = new(ControlPlaneDataPlaneTargetExternal)
		**out = **in
	}
	if in.Ref != nil {
		in, out := &in.Ref, &out.Ref
		*out = new(ControlPlaneDataPlaneTargetRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneDataPlaneTarget.
func (in *ControlPlaneDataPlaneTarget) DeepCopy() *ControlPlaneDataPlaneTarget {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneDataPlaneTarget)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneDataPlaneTargetExternal) DeepCopyInto(out *ControlPlaneDataPlaneTargetExternal) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneDataPlaneTargetExternal.
func (in *ControlPlaneDataPlaneTargetExternal) DeepCopy() *ControlPlaneDataPlaneTargetExternal {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneDataPlaneTargetExternal)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneDataPlaneTargetRef) DeepCopyInto(out *ControlPlaneDataPlaneTargetRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneDataPlaneTargetRef.
func (in *ControlPlaneDataPlaneTargetRef) DeepCopy() *ControlPlaneDataPlaneTargetRef {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneDataPlaneTargetRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneFeatureGate) DeepCopyInto(out *ControlPlaneFeatureGate) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneFeatureGate.
func (in *ControlPlaneFeatureGate) DeepCopy() *ControlPlaneFeatureGate {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneFeatureGate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneList) DeepCopyInto(out *ControlPlaneList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ControlPlane, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneList.
func (in *ControlPlaneList) DeepCopy() *ControlPlaneList {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ControlPlaneList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneOptions) DeepCopyInto(out *ControlPlaneOptions) {
	*out = *in
	in.DataPlane.DeepCopyInto(&out.DataPlane)
	if in.Extensions != nil {
		in, out := &in.Extensions, &out.Extensions
		*out = make([]v1alpha1.ExtensionRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.WatchNamespaces != nil {
		in, out := &in.WatchNamespaces, &out.WatchNamespaces
		*out = new(v1beta1.WatchNamespaces)
		(*in).DeepCopyInto(*out)
	}
	if in.FeatureGates != nil {
		in, out := &in.FeatureGates, &out.FeatureGates
		*out = make([]ControlPlaneFeatureGate, len(*in))
		copy(*out, *in)
	}
	if in.Controllers != nil {
		in, out := &in.Controllers, &out.Controllers
		*out = make([]ControlPlaneController, len(*in))
		copy(*out, *in)
	}
	if in.AdminAPI != nil {
		in, out := &in.AdminAPI, &out.AdminAPI
		*out = new(ControlPlaneAdminAPI)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneOptions.
func (in *ControlPlaneOptions) DeepCopy() *ControlPlaneOptions {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneOptions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneSpec) DeepCopyInto(out *ControlPlaneSpec) {
	*out = *in
	in.ControlPlaneOptions.DeepCopyInto(&out.ControlPlaneOptions)
	if in.IngressClass != nil {
		in, out := &in.IngressClass, &out.IngressClass
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneSpec.
func (in *ControlPlaneSpec) DeepCopy() *ControlPlaneSpec {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ControlPlaneStatus) DeepCopyInto(out *ControlPlaneStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.FeatureGates != nil {
		in, out := &in.FeatureGates, &out.FeatureGates
		*out = make([]ControlPlaneFeatureGate, len(*in))
		copy(*out, *in)
	}
	if in.Controllers != nil {
		in, out := &in.Controllers, &out.Controllers
		*out = make([]ControlPlaneController, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ControlPlaneStatus.
func (in *ControlPlaneStatus) DeepCopy() *ControlPlaneStatus {
	if in == nil {
		return nil
	}
	out := new(ControlPlaneStatus)
	in.DeepCopyInto(out)
	return out
}
