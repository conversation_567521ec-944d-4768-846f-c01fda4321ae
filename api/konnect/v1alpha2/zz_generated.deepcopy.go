//go:build !ignore_autogenerated

/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha2

import (
	"github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CertificateSecret) DeepCopyInto(out *CertificateSecret) {
	*out = *in
	if in.Provisioning != nil {
		in, out := &in.Provisioning, &out.Provisioning
		*out = new(ProvisioningMethod)
		**out = **in
	}
	if in.CertificateSecretRef != nil {
		in, out := &in.CertificateSecretRef, &out.CertificateSecretRef
		*out = new(SecretRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CertificateSecret.
func (in *CertificateSecret) DeepCopy() *CertificateSecret {
	if in == nil {
		return nil
	}
	out := new(CertificateSecret)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DataPlaneClientAuthStatus) DeepCopyInto(out *DataPlaneClientAuthStatus) {
	*out = *in
	if in.CertificateSecretRef != nil {
		in, out := &in.CertificateSecretRef, &out.CertificateSecretRef
		*out = new(SecretRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DataPlaneClientAuthStatus.
func (in *DataPlaneClientAuthStatus) DeepCopy() *DataPlaneClientAuthStatus {
	if in == nil {
		return nil
	}
	out := new(DataPlaneClientAuthStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEndpoints) DeepCopyInto(out *KonnectEndpoints) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEndpoints.
func (in *KonnectEndpoints) DeepCopy() *KonnectEndpoints {
	if in == nil {
		return nil
	}
	out := new(KonnectEndpoints)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtension) DeepCopyInto(out *KonnectExtension) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtension.
func (in *KonnectExtension) DeepCopy() *KonnectExtension {
	if in == nil {
		return nil
	}
	out := new(KonnectExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectExtension) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionClientAuth) DeepCopyInto(out *KonnectExtensionClientAuth) {
	*out = *in
	in.CertificateSecret.DeepCopyInto(&out.CertificateSecret)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionClientAuth.
func (in *KonnectExtensionClientAuth) DeepCopy() *KonnectExtensionClientAuth {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionClientAuth)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionControlPlane) DeepCopyInto(out *KonnectExtensionControlPlane) {
	*out = *in
	in.Ref.DeepCopyInto(&out.Ref)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionControlPlane.
func (in *KonnectExtensionControlPlane) DeepCopy() *KonnectExtensionControlPlane {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionControlPlane)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionControlPlaneStatus) DeepCopyInto(out *KonnectExtensionControlPlaneStatus) {
	*out = *in
	out.Endpoints = in.Endpoints
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionControlPlaneStatus.
func (in *KonnectExtensionControlPlaneStatus) DeepCopy() *KonnectExtensionControlPlaneStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionControlPlaneStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionDataPlane) DeepCopyInto(out *KonnectExtensionDataPlane) {
	*out = *in
	if in.Labels != nil {
		in, out := &in.Labels, &out.Labels
		*out = make(map[string]DataPlaneLabelValue, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionDataPlane.
func (in *KonnectExtensionDataPlane) DeepCopy() *KonnectExtensionDataPlane {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionDataPlane)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionKonnectSpec) DeepCopyInto(out *KonnectExtensionKonnectSpec) {
	*out = *in
	in.ControlPlane.DeepCopyInto(&out.ControlPlane)
	if in.DataPlane != nil {
		in, out := &in.DataPlane, &out.DataPlane
		*out = new(KonnectExtensionDataPlane)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionKonnectSpec.
func (in *KonnectExtensionKonnectSpec) DeepCopy() *KonnectExtensionKonnectSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionKonnectSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionList) DeepCopyInto(out *KonnectExtensionList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectExtension, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionList.
func (in *KonnectExtensionList) DeepCopy() *KonnectExtensionList {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectExtensionList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionSpec) DeepCopyInto(out *KonnectExtensionSpec) {
	*out = *in
	in.Konnect.DeepCopyInto(&out.Konnect)
	if in.ClientAuth != nil {
		in, out := &in.ClientAuth, &out.ClientAuth
		*out = new(KonnectExtensionClientAuth)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionSpec.
func (in *KonnectExtensionSpec) DeepCopy() *KonnectExtensionSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectExtensionStatus) DeepCopyInto(out *KonnectExtensionStatus) {
	*out = *in
	if in.DataPlaneRefs != nil {
		in, out := &in.DataPlaneRefs, &out.DataPlaneRefs
		*out = make([]v1alpha1.NamespacedRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ControlPlaneRefs != nil {
		in, out := &in.ControlPlaneRefs, &out.ControlPlaneRefs
		*out = make([]v1alpha1.NamespacedRef, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.DataPlaneClientAuth != nil {
		in, out := &in.DataPlaneClientAuth, &out.DataPlaneClientAuth
		*out = new(DataPlaneClientAuthStatus)
		(*in).DeepCopyInto(*out)
	}
	if in.Konnect != nil {
		in, out := &in.Konnect, &out.Konnect
		*out = new(KonnectExtensionControlPlaneStatus)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]v1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectExtensionStatus.
func (in *KonnectExtensionStatus) DeepCopy() *KonnectExtensionStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectExtensionStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecretRef) DeepCopyInto(out *SecretRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecretRef.
func (in *SecretRef) DeepCopy() *SecretRef {
	if in == nil {
		return nil
	}
	out := new(SecretRef)
	in.DeepCopyInto(out)
	return out
}
