package v1alpha2

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.


// GetTypeName returns the KonnectExtension Kind name
func (obj KonnectExtension) GetTypeName() string {
	return "KonnectExtension"
}

// GetConditions returns the Status Conditions
func (obj *KonnectExtension) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KonnectExtension) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

// SetControlPlaneRef sets the ControlPlaneRef.
func (obj *KonnectExtension) SetControlPlaneRef(ref *commonv1alpha1.KonnectExtensionControlPlaneRef) {
	if ref == nil {
		obj.Spec.Konnect.ControlPlane.Ref = commonv1alpha1.KonnectExtensionControlPlaneRef{}
		return
	}
	obj.Spec.Konnect.ControlPlane.Ref = *ref
}

// GetControlPlaneRef returns the ControlPlaneRef.
func (obj *KonnectExtension) GetControlPlaneRef() *commonv1alpha1.KonnectExtensionControlPlaneRef {
	return &obj.Spec.Konnect.ControlPlane.Ref
}
