package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.



// GetKonnectStatus returns the Konnect status contained in the KonnectCloudGatewayDataPlaneGroupConfiguration status.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) GetKonnectStatus() *KonnectEntityStatus {
	return &obj.Status.KonnectEntityStatusWithControlPlaneRef.KonnectEntityStatus
}

// GetKonnectID returns the Konnect ID in the KonnectCloudGatewayDataPlaneGroupConfiguration status.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) GetKonnectID() string {
	return obj.Status.ID
}

// SetKonnectID sets the Konnect ID in the KonnectCloudGatewayDataPlaneGroupConfiguration status.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) SetKonnectID(id string) {
	obj.Status.ID = id
}

// GetControlPlaneID returns the ControlPlane ID in the KonnectCloudGatewayDataPlaneGroupConfiguration status.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) GetControlPlaneID() string {
	return obj.Status.ControlPlaneID
}

// SetControlPlaneID sets the ControlPlane ID in the KonnectCloudGatewayDataPlaneGroupConfiguration status.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) SetControlPlaneID(id string) {
	obj.Status.ControlPlaneID = id
}

// GetTypeName returns the KonnectCloudGatewayDataPlaneGroupConfiguration Kind name
func (obj KonnectCloudGatewayDataPlaneGroupConfiguration) GetTypeName() string {
	return "KonnectCloudGatewayDataPlaneGroupConfiguration"
}

// GetConditions returns the Status Conditions
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

// SetControlPlaneRef sets the ControlPlaneRef.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) SetControlPlaneRef(ref *commonv1alpha1.ControlPlaneRef) {
	if ref == nil {
		obj.Spec.ControlPlaneRef = commonv1alpha1.ControlPlaneRef{}
		return
	}
	obj.Spec.ControlPlaneRef = *ref
}

// GetControlPlaneRef returns the ControlPlaneRef.
func (obj *KonnectCloudGatewayDataPlaneGroupConfiguration) GetControlPlaneRef() *commonv1alpha1.ControlPlaneRef {
	return &obj.Spec.ControlPlaneRef
}
