package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.


// GetKonnectStatus returns the Konnect status contained in the KonnectCloudGatewayTransitGateway status.
func (obj *KonnectCloudGatewayTransitGateway) GetKonnectStatus() *KonnectEntityStatus {
	return &obj.Status.KonnectEntityStatusWithNetworkRef.KonnectEntityStatus
}
// GetKonnectID returns the Konnect ID in the KonnectCloudGatewayTransitGateway status.
func (obj *KonnectCloudGatewayTransitGateway) GetKonnectID() string {
	return obj.Status.ID
}

// SetKonnectID sets the Konnect ID in the KonnectCloudGatewayTransitGateway status.
func (obj *KonnectCloudGatewayTransitGateway) SetKonnectID(id string) {
	obj.Status.ID = id
}
// GetTypeName returns the KonnectCloudGatewayTransitGateway Kind name
func (obj KonnectCloudGatewayTransitGateway) GetTypeName() string {
	return "KonnectCloudGatewayTransitGateway"
}

// GetConditions returns the Status Conditions
func (obj *KonnectCloudGatewayTransitGateway) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KonnectCloudGatewayTransitGateway) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}


// GetNetworkID returns Konnect network ID in the KonnectCloudGatewayTransitGateway status.
func (obj *KonnectCloudGatewayTransitGateway) GetNetworkID() string {
	return obj.Status.NetworkID
}

// SetNetworkID returns the Konnect network ID in the KonnectCloudGatewayTransitGateway status.
func (obj *KonnectCloudGatewayTransitGateway) SetNetworkID(id string) {
	obj.Status.NetworkID = id
}
