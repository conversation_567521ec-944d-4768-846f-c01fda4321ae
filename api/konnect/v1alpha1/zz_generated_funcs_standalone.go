package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.

// GetKonnectStatus returns the Konnect status contained in the KonnectGatewayControlPlane status.
func (obj *KonnectGatewayControlPlane) GetKonnectStatus() *KonnectEntityStatus {
	return &obj.Status.KonnectEntityStatus
}

// GetKonnectID returns the Konnect ID in the KonnectGatewayControlPlane status.
func (obj *KonnectGatewayControlPlane) GetKonnectID() string {
	return obj.Status.ID
}

// SetKonnectID sets the Konnect ID in the KonnectGatewayControlPlane status.
func (obj *KonnectGatewayControlPlane) SetKonnectID(id string) {
	obj.Status.ID = id
}
// GetTypeName returns the KonnectGatewayControlPlane Kind name
func (obj KonnectGatewayControlPlane) GetTypeName() string {
	return "KonnectGatewayControlPlane"
}

// GetConditions returns the Status Conditions
func (obj *KonnectGatewayControlPlane) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KonnectGatewayControlPlane) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

// GetTypeName returns the KonnectAPIAuthConfiguration Kind name
func (obj KonnectAPIAuthConfiguration) GetTypeName() string {
	return "KonnectAPIAuthConfiguration"
}

// GetConditions returns the Status Conditions
func (obj *KonnectAPIAuthConfiguration) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KonnectAPIAuthConfiguration) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}

// GetKonnectStatus returns the Konnect status contained in the KonnectCloudGatewayNetwork status.
func (obj *KonnectCloudGatewayNetwork) GetKonnectStatus() *KonnectEntityStatus {
	return &obj.Status.KonnectEntityStatus
}

// GetKonnectID returns the Konnect ID in the KonnectCloudGatewayNetwork status.
func (obj *KonnectCloudGatewayNetwork) GetKonnectID() string {
	return obj.Status.ID
}

// SetKonnectID sets the Konnect ID in the KonnectCloudGatewayNetwork status.
func (obj *KonnectCloudGatewayNetwork) SetKonnectID(id string) {
	obj.Status.ID = id
}
// GetTypeName returns the KonnectCloudGatewayNetwork Kind name
func (obj KonnectCloudGatewayNetwork) GetTypeName() string {
	return "KonnectCloudGatewayNetwork"
}

// GetConditions returns the Status Conditions
func (obj *KonnectCloudGatewayNetwork) GetConditions() []metav1.Condition {
	return obj.Status.Conditions
}

// SetConditions sets the Status Conditions
func (obj *KonnectCloudGatewayNetwork) SetConditions(conditions []metav1.Condition) {
	obj.Status.Conditions = conditions
}
