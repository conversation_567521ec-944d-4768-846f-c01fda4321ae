//go:build !ignore_autogenerated

/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	"github.com/Kong/sdk-konnect-go/models/components"
	commonv1alpha1 "github.com/kong/kubernetes-configuration/api/common/v1alpha1"
	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AWSTransitGateway) DeepCopyInto(out *AWSTransitGateway) {
	*out = *in
	if in.DNSConfig != nil {
		in, out := &in.DNSConfig, &out.DNSConfig
		*out = make([]TransitGatewayDNSConfig, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.CIDRBlocks != nil {
		in, out := &in.CIDRBlocks, &out.CIDRBlocks
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	out.AttachmentConfig = in.AttachmentConfig
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AWSTransitGateway.
func (in *AWSTransitGateway) DeepCopy() *AWSTransitGateway {
	if in == nil {
		return nil
	}
	out := new(AWSTransitGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AwsTransitGatewayAttachmentConfig) DeepCopyInto(out *AwsTransitGatewayAttachmentConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AwsTransitGatewayAttachmentConfig.
func (in *AwsTransitGatewayAttachmentConfig) DeepCopy() *AwsTransitGatewayAttachmentConfig {
	if in == nil {
		return nil
	}
	out := new(AwsTransitGatewayAttachmentConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AzureTransitGateway) DeepCopyInto(out *AzureTransitGateway) {
	*out = *in
	if in.DNSConfig != nil {
		in, out := &in.DNSConfig, &out.DNSConfig
		*out = make([]TransitGatewayDNSConfig, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	out.AttachmentConfig = in.AttachmentConfig
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AzureTransitGateway.
func (in *AzureTransitGateway) DeepCopy() *AzureTransitGateway {
	if in == nil {
		return nil
	}
	out := new(AzureTransitGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AzureVNETPeeringAttachmentConfig) DeepCopyInto(out *AzureVNETPeeringAttachmentConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AzureVNETPeeringAttachmentConfig.
func (in *AzureVNETPeeringAttachmentConfig) DeepCopy() *AzureVNETPeeringAttachmentConfig {
	if in == nil {
		return nil
	}
	out := new(AzureVNETPeeringAttachmentConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConfigurationDataPlaneGroupAutoscale) DeepCopyInto(out *ConfigurationDataPlaneGroupAutoscale) {
	*out = *in
	if in.Static != nil {
		in, out := &in.Static, &out.Static
		*out = new(ConfigurationDataPlaneGroupAutoscaleStatic)
		**out = **in
	}
	if in.Autopilot != nil {
		in, out := &in.Autopilot, &out.Autopilot
		*out = new(ConfigurationDataPlaneGroupAutoscaleAutopilot)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConfigurationDataPlaneGroupAutoscale.
func (in *ConfigurationDataPlaneGroupAutoscale) DeepCopy() *ConfigurationDataPlaneGroupAutoscale {
	if in == nil {
		return nil
	}
	out := new(ConfigurationDataPlaneGroupAutoscale)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConfigurationDataPlaneGroupAutoscaleAutopilot) DeepCopyInto(out *ConfigurationDataPlaneGroupAutoscaleAutopilot) {
	*out = *in
	if in.MaxRps != nil {
		in, out := &in.MaxRps, &out.MaxRps
		*out = new(int64)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConfigurationDataPlaneGroupAutoscaleAutopilot.
func (in *ConfigurationDataPlaneGroupAutoscaleAutopilot) DeepCopy() *ConfigurationDataPlaneGroupAutoscaleAutopilot {
	if in == nil {
		return nil
	}
	out := new(ConfigurationDataPlaneGroupAutoscaleAutopilot)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConfigurationDataPlaneGroupAutoscaleStatic) DeepCopyInto(out *ConfigurationDataPlaneGroupAutoscaleStatic) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConfigurationDataPlaneGroupAutoscaleStatic.
func (in *ConfigurationDataPlaneGroupAutoscaleStatic) DeepCopy() *ConfigurationDataPlaneGroupAutoscaleStatic {
	if in == nil {
		return nil
	}
	out := new(ConfigurationDataPlaneGroupAutoscaleStatic)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ConfigurationDataPlaneGroupEnvironmentField) DeepCopyInto(out *ConfigurationDataPlaneGroupEnvironmentField) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ConfigurationDataPlaneGroupEnvironmentField.
func (in *ConfigurationDataPlaneGroupEnvironmentField) DeepCopy() *ConfigurationDataPlaneGroupEnvironmentField {
	if in == nil {
		return nil
	}
	out := new(ConfigurationDataPlaneGroupEnvironmentField)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CreateControlPlaneRequest) DeepCopyInto(out *CreateControlPlaneRequest) {
	*out = *in
	if in.Name != nil {
		in, out := &in.Name, &out.Name
		*out = new(string)
		**out = **in
	}
	if in.Description != nil {
		in, out := &in.Description, &out.Description
		*out = new(string)
		**out = **in
	}
	if in.ClusterType != nil {
		in, out := &in.ClusterType, &out.ClusterType
		*out = new(components.CreateControlPlaneRequestClusterType)
		**out = **in
	}
	if in.AuthType != nil {
		in, out := &in.AuthType, &out.AuthType
		*out = new(components.AuthType)
		**out = **in
	}
	if in.CloudGateway != nil {
		in, out := &in.CloudGateway, &out.CloudGateway
		*out = new(bool)
		**out = **in
	}
	if in.ProxyUrls != nil {
		in, out := &in.ProxyUrls, &out.ProxyUrls
		*out = make([]components.ProxyURL, len(*in))
		copy(*out, *in)
	}
	if in.Labels != nil {
		in, out := &in.Labels, &out.Labels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CreateControlPlaneRequest.
func (in *CreateControlPlaneRequest) DeepCopy() *CreateControlPlaneRequest {
	if in == nil {
		return nil
	}
	out := new(CreateControlPlaneRequest)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectAPIAuthConfiguration) DeepCopyInto(out *KonnectAPIAuthConfiguration) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectAPIAuthConfiguration.
func (in *KonnectAPIAuthConfiguration) DeepCopy() *KonnectAPIAuthConfiguration {
	if in == nil {
		return nil
	}
	out := new(KonnectAPIAuthConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectAPIAuthConfiguration) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectAPIAuthConfigurationList) DeepCopyInto(out *KonnectAPIAuthConfigurationList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectAPIAuthConfiguration, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectAPIAuthConfigurationList.
func (in *KonnectAPIAuthConfigurationList) DeepCopy() *KonnectAPIAuthConfigurationList {
	if in == nil {
		return nil
	}
	out := new(KonnectAPIAuthConfigurationList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectAPIAuthConfigurationList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectAPIAuthConfigurationRef) DeepCopyInto(out *KonnectAPIAuthConfigurationRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectAPIAuthConfigurationRef.
func (in *KonnectAPIAuthConfigurationRef) DeepCopy() *KonnectAPIAuthConfigurationRef {
	if in == nil {
		return nil
	}
	out := new(KonnectAPIAuthConfigurationRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectAPIAuthConfigurationSpec) DeepCopyInto(out *KonnectAPIAuthConfigurationSpec) {
	*out = *in
	if in.SecretRef != nil {
		in, out := &in.SecretRef, &out.SecretRef
		*out = new(v1.SecretReference)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectAPIAuthConfigurationSpec.
func (in *KonnectAPIAuthConfigurationSpec) DeepCopy() *KonnectAPIAuthConfigurationSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectAPIAuthConfigurationSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectAPIAuthConfigurationStatus) DeepCopyInto(out *KonnectAPIAuthConfigurationStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectAPIAuthConfigurationStatus.
func (in *KonnectAPIAuthConfigurationStatus) DeepCopy() *KonnectAPIAuthConfigurationStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectAPIAuthConfigurationStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayDataPlaneGroupConfiguration) DeepCopyInto(out *KonnectCloudGatewayDataPlaneGroupConfiguration) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayDataPlaneGroupConfiguration.
func (in *KonnectCloudGatewayDataPlaneGroupConfiguration) DeepCopy() *KonnectCloudGatewayDataPlaneGroupConfiguration {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayDataPlaneGroupConfiguration)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectCloudGatewayDataPlaneGroupConfiguration) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationList) DeepCopyInto(out *KonnectCloudGatewayDataPlaneGroupConfigurationList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectCloudGatewayDataPlaneGroupConfiguration, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayDataPlaneGroupConfigurationList.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationList) DeepCopy() *KonnectCloudGatewayDataPlaneGroupConfigurationList {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayDataPlaneGroupConfigurationList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationSpec) DeepCopyInto(out *KonnectCloudGatewayDataPlaneGroupConfigurationSpec) {
	*out = *in
	if in.DataplaneGroups != nil {
		in, out := &in.DataplaneGroups, &out.DataplaneGroups
		*out = make([]KonnectConfigurationDataPlaneGroup, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.APIAccess != nil {
		in, out := &in.APIAccess, &out.APIAccess
		*out = new(components.APIAccess)
		**out = **in
	}
	in.ControlPlaneRef.DeepCopyInto(&out.ControlPlaneRef)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayDataPlaneGroupConfigurationSpec.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationSpec) DeepCopy() *KonnectCloudGatewayDataPlaneGroupConfigurationSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayDataPlaneGroupConfigurationSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationStatus) DeepCopyInto(out *KonnectCloudGatewayDataPlaneGroupConfigurationStatus) {
	*out = *in
	out.KonnectEntityStatusWithControlPlaneRef = in.KonnectEntityStatusWithControlPlaneRef
	if in.DataPlaneGroups != nil {
		in, out := &in.DataPlaneGroups, &out.DataPlaneGroups
		*out = make([]KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayDataPlaneGroupConfigurationStatus.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationStatus) DeepCopy() *KonnectCloudGatewayDataPlaneGroupConfigurationStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayDataPlaneGroupConfigurationStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup) DeepCopyInto(out *KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup) {
	*out = *in
	if in.PrivateIPAddresses != nil {
		in, out := &in.PrivateIPAddresses, &out.PrivateIPAddresses
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.EgressIPAddresses != nil {
		in, out := &in.EgressIPAddresses, &out.EgressIPAddresses
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup.
func (in *KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup) DeepCopy() *KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayDataPlaneGroupConfigurationStatusGroup)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayNetwork) DeepCopyInto(out *KonnectCloudGatewayNetwork) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayNetwork.
func (in *KonnectCloudGatewayNetwork) DeepCopy() *KonnectCloudGatewayNetwork {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayNetwork)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectCloudGatewayNetwork) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayNetworkList) DeepCopyInto(out *KonnectCloudGatewayNetworkList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectCloudGatewayNetwork, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayNetworkList.
func (in *KonnectCloudGatewayNetworkList) DeepCopy() *KonnectCloudGatewayNetworkList {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayNetworkList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectCloudGatewayNetworkList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayNetworkSpec) DeepCopyInto(out *KonnectCloudGatewayNetworkSpec) {
	*out = *in
	if in.AvailabilityZones != nil {
		in, out := &in.AvailabilityZones, &out.AvailabilityZones
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.State != nil {
		in, out := &in.State, &out.State
		*out = new(components.NetworkCreateState)
		**out = **in
	}
	out.KonnectConfiguration = in.KonnectConfiguration
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayNetworkSpec.
func (in *KonnectCloudGatewayNetworkSpec) DeepCopy() *KonnectCloudGatewayNetworkSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayNetworkSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayNetworkStatus) DeepCopyInto(out *KonnectCloudGatewayNetworkStatus) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayNetworkStatus.
func (in *KonnectCloudGatewayNetworkStatus) DeepCopy() *KonnectCloudGatewayNetworkStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayNetworkStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayTransitGateway) DeepCopyInto(out *KonnectCloudGatewayTransitGateway) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayTransitGateway.
func (in *KonnectCloudGatewayTransitGateway) DeepCopy() *KonnectCloudGatewayTransitGateway {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayTransitGateway)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectCloudGatewayTransitGateway) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayTransitGatewayList) DeepCopyInto(out *KonnectCloudGatewayTransitGatewayList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectCloudGatewayTransitGateway, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayTransitGatewayList.
func (in *KonnectCloudGatewayTransitGatewayList) DeepCopy() *KonnectCloudGatewayTransitGatewayList {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayTransitGatewayList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectCloudGatewayTransitGatewayList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayTransitGatewaySpec) DeepCopyInto(out *KonnectCloudGatewayTransitGatewaySpec) {
	*out = *in
	in.NetworkRef.DeepCopyInto(&out.NetworkRef)
	in.KonnectTransitGatewayAPISpec.DeepCopyInto(&out.KonnectTransitGatewayAPISpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayTransitGatewaySpec.
func (in *KonnectCloudGatewayTransitGatewaySpec) DeepCopy() *KonnectCloudGatewayTransitGatewaySpec {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayTransitGatewaySpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectCloudGatewayTransitGatewayStatus) DeepCopyInto(out *KonnectCloudGatewayTransitGatewayStatus) {
	*out = *in
	out.KonnectEntityStatusWithNetworkRef = in.KonnectEntityStatusWithNetworkRef
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectCloudGatewayTransitGatewayStatus.
func (in *KonnectCloudGatewayTransitGatewayStatus) DeepCopy() *KonnectCloudGatewayTransitGatewayStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectCloudGatewayTransitGatewayStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectConfigurationDataPlaneGroup) DeepCopyInto(out *KonnectConfigurationDataPlaneGroup) {
	*out = *in
	in.NetworkRef.DeepCopyInto(&out.NetworkRef)
	in.Autoscale.DeepCopyInto(&out.Autoscale)
	if in.Environment != nil {
		in, out := &in.Environment, &out.Environment
		*out = make([]ConfigurationDataPlaneGroupEnvironmentField, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectConfigurationDataPlaneGroup.
func (in *KonnectConfigurationDataPlaneGroup) DeepCopy() *KonnectConfigurationDataPlaneGroup {
	if in == nil {
		return nil
	}
	out := new(KonnectConfigurationDataPlaneGroup)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEndpoints) DeepCopyInto(out *KonnectEndpoints) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEndpoints.
func (in *KonnectEndpoints) DeepCopy() *KonnectEndpoints {
	if in == nil {
		return nil
	}
	out := new(KonnectEndpoints)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatus) DeepCopyInto(out *KonnectEntityStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatus.
func (in *KonnectEntityStatus) DeepCopy() *KonnectEntityStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithControlPlaneAndCertificateRefs) DeepCopyInto(out *KonnectEntityStatusWithControlPlaneAndCertificateRefs) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithControlPlaneAndCertificateRefs.
func (in *KonnectEntityStatusWithControlPlaneAndCertificateRefs) DeepCopy() *KonnectEntityStatusWithControlPlaneAndCertificateRefs {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithControlPlaneAndCertificateRefs)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithControlPlaneAndConsumerRefs) DeepCopyInto(out *KonnectEntityStatusWithControlPlaneAndConsumerRefs) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithControlPlaneAndConsumerRefs.
func (in *KonnectEntityStatusWithControlPlaneAndConsumerRefs) DeepCopy() *KonnectEntityStatusWithControlPlaneAndConsumerRefs {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithControlPlaneAndConsumerRefs)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithControlPlaneAndKeySetRef) DeepCopyInto(out *KonnectEntityStatusWithControlPlaneAndKeySetRef) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithControlPlaneAndKeySetRef.
func (in *KonnectEntityStatusWithControlPlaneAndKeySetRef) DeepCopy() *KonnectEntityStatusWithControlPlaneAndKeySetRef {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithControlPlaneAndKeySetRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithControlPlaneAndServiceRefs) DeepCopyInto(out *KonnectEntityStatusWithControlPlaneAndServiceRefs) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithControlPlaneAndServiceRefs.
func (in *KonnectEntityStatusWithControlPlaneAndServiceRefs) DeepCopy() *KonnectEntityStatusWithControlPlaneAndServiceRefs {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithControlPlaneAndServiceRefs)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithControlPlaneAndUpstreamRefs) DeepCopyInto(out *KonnectEntityStatusWithControlPlaneAndUpstreamRefs) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithControlPlaneAndUpstreamRefs.
func (in *KonnectEntityStatusWithControlPlaneAndUpstreamRefs) DeepCopy() *KonnectEntityStatusWithControlPlaneAndUpstreamRefs {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithControlPlaneAndUpstreamRefs)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithControlPlaneRef) DeepCopyInto(out *KonnectEntityStatusWithControlPlaneRef) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithControlPlaneRef.
func (in *KonnectEntityStatusWithControlPlaneRef) DeepCopy() *KonnectEntityStatusWithControlPlaneRef {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithControlPlaneRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectEntityStatusWithNetworkRef) DeepCopyInto(out *KonnectEntityStatusWithNetworkRef) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectEntityStatusWithNetworkRef.
func (in *KonnectEntityStatusWithNetworkRef) DeepCopy() *KonnectEntityStatusWithNetworkRef {
	if in == nil {
		return nil
	}
	out := new(KonnectEntityStatusWithNetworkRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectGatewayControlPlane) DeepCopyInto(out *KonnectGatewayControlPlane) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectGatewayControlPlane.
func (in *KonnectGatewayControlPlane) DeepCopy() *KonnectGatewayControlPlane {
	if in == nil {
		return nil
	}
	out := new(KonnectGatewayControlPlane)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectGatewayControlPlane) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectGatewayControlPlaneList) DeepCopyInto(out *KonnectGatewayControlPlaneList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KonnectGatewayControlPlane, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectGatewayControlPlaneList.
func (in *KonnectGatewayControlPlaneList) DeepCopy() *KonnectGatewayControlPlaneList {
	if in == nil {
		return nil
	}
	out := new(KonnectGatewayControlPlaneList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KonnectGatewayControlPlaneList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectGatewayControlPlaneSpec) DeepCopyInto(out *KonnectGatewayControlPlaneSpec) {
	*out = *in
	in.CreateControlPlaneRequest.DeepCopyInto(&out.CreateControlPlaneRequest)
	if in.Mirror != nil {
		in, out := &in.Mirror, &out.Mirror
		*out = new(MirrorSpec)
		**out = **in
	}
	if in.Source != nil {
		in, out := &in.Source, &out.Source
		*out = new(commonv1alpha1.EntitySource)
		**out = **in
	}
	if in.Members != nil {
		in, out := &in.Members, &out.Members
		*out = make([]v1.LocalObjectReference, len(*in))
		copy(*out, *in)
	}
	out.KonnectConfiguration = in.KonnectConfiguration
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectGatewayControlPlaneSpec.
func (in *KonnectGatewayControlPlaneSpec) DeepCopy() *KonnectGatewayControlPlaneSpec {
	if in == nil {
		return nil
	}
	out := new(KonnectGatewayControlPlaneSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectGatewayControlPlaneStatus) DeepCopyInto(out *KonnectGatewayControlPlaneStatus) {
	*out = *in
	out.KonnectEntityStatus = in.KonnectEntityStatus
	if in.Endpoints != nil {
		in, out := &in.Endpoints, &out.Endpoints
		*out = new(KonnectEndpoints)
		**out = **in
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectGatewayControlPlaneStatus.
func (in *KonnectGatewayControlPlaneStatus) DeepCopy() *KonnectGatewayControlPlaneStatus {
	if in == nil {
		return nil
	}
	out := new(KonnectGatewayControlPlaneStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KonnectTransitGatewayAPISpec) DeepCopyInto(out *KonnectTransitGatewayAPISpec) {
	*out = *in
	if in.AWSTransitGateway != nil {
		in, out := &in.AWSTransitGateway, &out.AWSTransitGateway
		*out = new(AWSTransitGateway)
		(*in).DeepCopyInto(*out)
	}
	if in.AzureTransitGateway != nil {
		in, out := &in.AzureTransitGateway, &out.AzureTransitGateway
		*out = new(AzureTransitGateway)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KonnectTransitGatewayAPISpec.
func (in *KonnectTransitGatewayAPISpec) DeepCopy() *KonnectTransitGatewayAPISpec {
	if in == nil {
		return nil
	}
	out := new(KonnectTransitGatewayAPISpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MirrorKonnect) DeepCopyInto(out *MirrorKonnect) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MirrorKonnect.
func (in *MirrorKonnect) DeepCopy() *MirrorKonnect {
	if in == nil {
		return nil
	}
	out := new(MirrorKonnect)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *MirrorSpec) DeepCopyInto(out *MirrorSpec) {
	*out = *in
	out.Konnect = in.Konnect
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new MirrorSpec.
func (in *MirrorSpec) DeepCopy() *MirrorSpec {
	if in == nil {
		return nil
	}
	out := new(MirrorSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TransitGatewayDNSConfig) DeepCopyInto(out *TransitGatewayDNSConfig) {
	*out = *in
	if in.RemoteDNSServerIPAddresses != nil {
		in, out := &in.RemoteDNSServerIPAddresses, &out.RemoteDNSServerIPAddresses
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.DomainProxyList != nil {
		in, out := &in.DomainProxyList, &out.DomainProxyList
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TransitGatewayDNSConfig.
func (in *TransitGatewayDNSConfig) DeepCopy() *TransitGatewayDNSConfig {
	if in == nil {
		return nil
	}
	out := new(TransitGatewayDNSConfig)
	in.DeepCopyInto(out)
	return out
}
