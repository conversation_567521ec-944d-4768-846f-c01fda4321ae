package v1alpha1

// Code generated by scripts/apitypes-funcs/main.go; DO NOT EDIT.

// GetItems returns the list of KonnectGatewayControlPlane items.
func (obj KonnectGatewayControlPlaneList) GetItems() []KonnectGatewayControlPlane {
	return obj.Items
}

// GetItems returns the list of KonnectAPIAuthConfiguration items.
func (obj KonnectAPIAuthConfigurationList) GetItems() []KonnectAPIAuthConfiguration {
	return obj.Items
}

// GetItems returns the list of KonnectCloudGatewayNetwork items.
func (obj KonnectCloudGatewayNetworkList) GetItems() []KonnectCloudGatewayNetwork {
	return obj.Items
}

// GetItems returns the list of KonnectCloudGatewayDataPlaneGroupConfiguration items.
func (obj KonnectCloudGatewayDataPlaneGroupConfigurationList) GetItems() []KonnectCloudGatewayDataPlaneGroupConfiguration {
	return obj.Items
}

// GetItems returns the list of KonnectCloudGatewayTransitGateway items.
func (obj KonnectCloudGatewayTransitGatewayList) GetItems() []KonnectCloudGatewayTransitGateway {
	return obj.Items
}
