/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package clientset

import (
	fmt "fmt"
	http "net/http"

	configurationv1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1"
	configurationv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1alpha1"
	configurationv1beta1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1beta1"
	gatewayoperatorv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v1alpha1"
	gatewayoperatorv1beta1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v1beta1"
	gatewayoperatorv2alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v2alpha1"
	incubatorv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/incubator/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/konnect/v1alpha1"
	konnectv1alpha2 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/konnect/v1alpha2"
	discovery "k8s.io/client-go/discovery"
	rest "k8s.io/client-go/rest"
	flowcontrol "k8s.io/client-go/util/flowcontrol"
)

type Interface interface {
	Discovery() discovery.DiscoveryInterface
	ConfigurationV1() configurationv1.ConfigurationV1Interface
	ConfigurationV1alpha1() configurationv1alpha1.ConfigurationV1alpha1Interface
	ConfigurationV1beta1() configurationv1beta1.ConfigurationV1beta1Interface
	GatewayOperatorV1alpha1() gatewayoperatorv1alpha1.GatewayOperatorV1alpha1Interface
	GatewayOperatorV1beta1() gatewayoperatorv1beta1.GatewayOperatorV1beta1Interface
	GatewayOperatorV2alpha1() gatewayoperatorv2alpha1.GatewayOperatorV2alpha1Interface
	IncubatorV1alpha1() incubatorv1alpha1.IncubatorV1alpha1Interface
	KonnectV1alpha1() konnectv1alpha1.KonnectV1alpha1Interface
	KonnectV1alpha2() konnectv1alpha2.KonnectV1alpha2Interface
}

// Clientset contains the clients for groups.
type Clientset struct {
	*discovery.DiscoveryClient
	configurationV1         *configurationv1.ConfigurationV1Client
	configurationV1alpha1   *configurationv1alpha1.ConfigurationV1alpha1Client
	configurationV1beta1    *configurationv1beta1.ConfigurationV1beta1Client
	gatewayOperatorV1alpha1 *gatewayoperatorv1alpha1.GatewayOperatorV1alpha1Client
	gatewayOperatorV1beta1  *gatewayoperatorv1beta1.GatewayOperatorV1beta1Client
	gatewayOperatorV2alpha1 *gatewayoperatorv2alpha1.GatewayOperatorV2alpha1Client
	incubatorV1alpha1       *incubatorv1alpha1.IncubatorV1alpha1Client
	konnectV1alpha1         *konnectv1alpha1.KonnectV1alpha1Client
	konnectV1alpha2         *konnectv1alpha2.KonnectV1alpha2Client
}

// ConfigurationV1 retrieves the ConfigurationV1Client
func (c *Clientset) ConfigurationV1() configurationv1.ConfigurationV1Interface {
	return c.configurationV1
}

// ConfigurationV1alpha1 retrieves the ConfigurationV1alpha1Client
func (c *Clientset) ConfigurationV1alpha1() configurationv1alpha1.ConfigurationV1alpha1Interface {
	return c.configurationV1alpha1
}

// ConfigurationV1beta1 retrieves the ConfigurationV1beta1Client
func (c *Clientset) ConfigurationV1beta1() configurationv1beta1.ConfigurationV1beta1Interface {
	return c.configurationV1beta1
}

// GatewayOperatorV1alpha1 retrieves the GatewayOperatorV1alpha1Client
func (c *Clientset) GatewayOperatorV1alpha1() gatewayoperatorv1alpha1.GatewayOperatorV1alpha1Interface {
	return c.gatewayOperatorV1alpha1
}

// GatewayOperatorV1beta1 retrieves the GatewayOperatorV1beta1Client
func (c *Clientset) GatewayOperatorV1beta1() gatewayoperatorv1beta1.GatewayOperatorV1beta1Interface {
	return c.gatewayOperatorV1beta1
}

// GatewayOperatorV2alpha1 retrieves the GatewayOperatorV2alpha1Client
func (c *Clientset) GatewayOperatorV2alpha1() gatewayoperatorv2alpha1.GatewayOperatorV2alpha1Interface {
	return c.gatewayOperatorV2alpha1
}

// IncubatorV1alpha1 retrieves the IncubatorV1alpha1Client
func (c *Clientset) IncubatorV1alpha1() incubatorv1alpha1.IncubatorV1alpha1Interface {
	return c.incubatorV1alpha1
}

// KonnectV1alpha1 retrieves the KonnectV1alpha1Client
func (c *Clientset) KonnectV1alpha1() konnectv1alpha1.KonnectV1alpha1Interface {
	return c.konnectV1alpha1
}

// KonnectV1alpha2 retrieves the KonnectV1alpha2Client
func (c *Clientset) KonnectV1alpha2() konnectv1alpha2.KonnectV1alpha2Interface {
	return c.konnectV1alpha2
}

// Discovery retrieves the DiscoveryClient
func (c *Clientset) Discovery() discovery.DiscoveryInterface {
	if c == nil {
		return nil
	}
	return c.DiscoveryClient
}

// NewForConfig creates a new Clientset for the given config.
// If config's RateLimiter is not set and QPS and Burst are acceptable,
// NewForConfig will generate a rate-limiter in configShallowCopy.
// NewForConfig is equivalent to NewForConfigAndClient(c, httpClient),
// where httpClient was generated with rest.HTTPClientFor(c).
func NewForConfig(c *rest.Config) (*Clientset, error) {
	configShallowCopy := *c

	if configShallowCopy.UserAgent == "" {
		configShallowCopy.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	// share the transport between all clients
	httpClient, err := rest.HTTPClientFor(&configShallowCopy)
	if err != nil {
		return nil, err
	}

	return NewForConfigAndClient(&configShallowCopy, httpClient)
}

// NewForConfigAndClient creates a new Clientset for the given config and http client.
// Note the http client provided takes precedence over the configured transport values.
// If config's RateLimiter is not set and QPS and Burst are acceptable,
// NewForConfigAndClient will generate a rate-limiter in configShallowCopy.
func NewForConfigAndClient(c *rest.Config, httpClient *http.Client) (*Clientset, error) {
	configShallowCopy := *c
	if configShallowCopy.RateLimiter == nil && configShallowCopy.QPS > 0 {
		if configShallowCopy.Burst <= 0 {
			return nil, fmt.Errorf("burst is required to be greater than 0 when RateLimiter is not set and QPS is set to greater than 0")
		}
		configShallowCopy.RateLimiter = flowcontrol.NewTokenBucketRateLimiter(configShallowCopy.QPS, configShallowCopy.Burst)
	}

	var cs Clientset
	var err error
	cs.configurationV1, err = configurationv1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.configurationV1alpha1, err = configurationv1alpha1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.configurationV1beta1, err = configurationv1beta1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.gatewayOperatorV1alpha1, err = gatewayoperatorv1alpha1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.gatewayOperatorV1beta1, err = gatewayoperatorv1beta1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.gatewayOperatorV2alpha1, err = gatewayoperatorv2alpha1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.incubatorV1alpha1, err = incubatorv1alpha1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.konnectV1alpha1, err = konnectv1alpha1.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	cs.konnectV1alpha2, err = konnectv1alpha2.NewForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}

	cs.DiscoveryClient, err = discovery.NewDiscoveryClientForConfigAndClient(&configShallowCopy, httpClient)
	if err != nil {
		return nil, err
	}
	return &cs, nil
}

// NewForConfigOrDie creates a new Clientset for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *Clientset {
	cs, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return cs
}

// New creates a new Clientset for the given RESTClient.
func New(c rest.Interface) *Clientset {
	var cs Clientset
	cs.configurationV1 = configurationv1.New(c)
	cs.configurationV1alpha1 = configurationv1alpha1.New(c)
	cs.configurationV1beta1 = configurationv1beta1.New(c)
	cs.gatewayOperatorV1alpha1 = gatewayoperatorv1alpha1.New(c)
	cs.gatewayOperatorV1beta1 = gatewayoperatorv1beta1.New(c)
	cs.gatewayOperatorV2alpha1 = gatewayoperatorv2alpha1.New(c)
	cs.incubatorV1alpha1 = incubatorv1alpha1.New(c)
	cs.konnectV1alpha1 = konnectv1alpha1.New(c)
	cs.konnectV1alpha2 = konnectv1alpha2.New(c)

	cs.DiscoveryClient = discovery.NewDiscoveryClient(c)
	return &cs
}
