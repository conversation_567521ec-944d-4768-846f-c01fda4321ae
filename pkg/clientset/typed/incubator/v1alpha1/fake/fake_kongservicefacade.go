/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/incubator/v1alpha1"
	incubatorv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/incubator/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongServiceFacades implements KongServiceFacadeInterface
type fakeKongServiceFacades struct {
	*gentype.FakeClientWithList[*v1alpha1.KongServiceFacade, *v1alpha1.KongServiceFacadeList]
	Fake *FakeIncubatorV1alpha1
}

func newFakeKongServiceFacades(fake *FakeIncubatorV1alpha1, namespace string) incubatorv1alpha1.KongServiceFacadeInterface {
	return &fakeKongServiceFacades{
		gentype.NewFakeClientWithList[*v1alpha1.KongServiceFacade, *v1alpha1.KongServiceFacadeList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("kongservicefacades"),
			v1alpha1.SchemeGroupVersion.WithKind("KongServiceFacade"),
			func() *v1alpha1.KongServiceFacade { return &v1alpha1.KongServiceFacade{} },
			func() *v1alpha1.KongServiceFacadeList { return &v1alpha1.KongServiceFacadeList{} },
			func(dst, src *v1alpha1.KongServiceFacadeList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.KongServiceFacadeList) []*v1alpha1.KongServiceFacade {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.KongServiceFacadeList, items []*v1alpha1.KongServiceFacade) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
