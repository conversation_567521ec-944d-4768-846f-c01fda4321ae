/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	context "context"

	configurationv1 "github.com/kong/kubernetes-configuration/api/configuration/v1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KongClusterPluginsGetter has a method to return a KongClusterPluginInterface.
// A group's client should implement this interface.
type KongClusterPluginsGetter interface {
	KongClusterPlugins() KongClusterPluginInterface
}

// KongClusterPluginInterface has methods to work with KongClusterPlugin resources.
type KongClusterPluginInterface interface {
	Create(ctx context.Context, kongClusterPlugin *configurationv1.KongClusterPlugin, opts metav1.CreateOptions) (*configurationv1.KongClusterPlugin, error)
	Update(ctx context.Context, kongClusterPlugin *configurationv1.KongClusterPlugin, opts metav1.UpdateOptions) (*configurationv1.KongClusterPlugin, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, kongClusterPlugin *configurationv1.KongClusterPlugin, opts metav1.UpdateOptions) (*configurationv1.KongClusterPlugin, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*configurationv1.KongClusterPlugin, error)
	List(ctx context.Context, opts metav1.ListOptions) (*configurationv1.KongClusterPluginList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *configurationv1.KongClusterPlugin, err error)
	KongClusterPluginExpansion
}

// kongClusterPlugins implements KongClusterPluginInterface
type kongClusterPlugins struct {
	*gentype.ClientWithList[*configurationv1.KongClusterPlugin, *configurationv1.KongClusterPluginList]
}

// newKongClusterPlugins returns a KongClusterPlugins
func newKongClusterPlugins(c *ConfigurationV1Client) *kongClusterPlugins {
	return &kongClusterPlugins{
		gentype.NewClientWithList[*configurationv1.KongClusterPlugin, *configurationv1.KongClusterPluginList](
			"kongclusterplugins",
			c.RESTClient(),
			scheme.ParameterCodec,
			"",
			func() *configurationv1.KongClusterPlugin { return &configurationv1.KongClusterPlugin{} },
			func() *configurationv1.KongClusterPluginList { return &configurationv1.KongClusterPluginList{} },
		),
	}
}
