/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/kong/kubernetes-configuration/api/configuration/v1"
	configurationv1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongPlugins implements KongPluginInterface
type fakeKongPlugins struct {
	*gentype.FakeClientWithList[*v1.KongPlugin, *v1.KongPluginList]
	Fake *FakeConfigurationV1
}

func newFakeKongPlugins(fake *FakeConfigurationV1, namespace string) configurationv1.KongPluginInterface {
	return &fakeKongPlugins{
		gentype.NewFakeClientWithList[*v1.KongPlugin, *v1.KongPluginList](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("kongplugins"),
			v1.SchemeGroupVersion.WithKind("KongPlugin"),
			func() *v1.KongPlugin { return &v1.KongPlugin{} },
			func() *v1.KongPluginList { return &v1.KongPluginList{} },
			func(dst, src *v1.KongPluginList) { dst.ListMeta = src.ListMeta },
			func(list *v1.KongPluginList) []*v1.KongPlugin { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.KongPluginList, items []*v1.KongPlugin) { list.Items = gentype.FromPointerSlice(items) },
		),
		fake,
	}
}
