/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/kong/kubernetes-configuration/api/configuration/v1"
	configurationv1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongIngresses implements KongIngressInterface
type fakeKongIngresses struct {
	*gentype.FakeClientWithList[*v1.KongIngress, *v1.KongIngressList]
	Fake *FakeConfigurationV1
}

func newFakeKongIngresses(fake *FakeConfigurationV1, namespace string) configurationv1.KongIngressInterface {
	return &fakeKongIngresses{
		gentype.NewFakeClientWithList[*v1.KongIngress, *v1.KongIngressList](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("kongingresses"),
			v1.SchemeGroupVersion.WithKind("KongIngress"),
			func() *v1.KongIngress { return &v1.KongIngress{} },
			func() *v1.KongIngressList { return &v1.KongIngressList{} },
			func(dst, src *v1.KongIngressList) { dst.ListMeta = src.ListMeta },
			func(list *v1.KongIngressList) []*v1.KongIngress { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.KongIngressList, items []*v1.KongIngress) { list.Items = gentype.FromPointerSlice(items) },
		),
		fake,
	}
}
