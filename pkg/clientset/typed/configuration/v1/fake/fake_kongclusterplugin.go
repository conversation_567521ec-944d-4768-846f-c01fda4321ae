/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/kong/kubernetes-configuration/api/configuration/v1"
	configurationv1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongClusterPlugins implements KongClusterPluginInterface
type fakeKongClusterPlugins struct {
	*gentype.FakeClientWithList[*v1.KongClusterPlugin, *v1.KongClusterPluginList]
	Fake *FakeConfigurationV1
}

func newFakeKongClusterPlugins(fake *FakeConfigurationV1) configurationv1.KongClusterPluginInterface {
	return &fakeKongClusterPlugins{
		gentype.NewFakeClientWithList[*v1.KongClusterPlugin, *v1.KongClusterPluginList](
			fake.Fake,
			"",
			v1.SchemeGroupVersion.WithResource("kongclusterplugins"),
			v1.SchemeGroupVersion.WithKind("KongClusterPlugin"),
			func() *v1.KongClusterPlugin { return &v1.KongClusterPlugin{} },
			func() *v1.KongClusterPluginList { return &v1.KongClusterPluginList{} },
			func(dst, src *v1.KongClusterPluginList) { dst.ListMeta = src.ListMeta },
			func(list *v1.KongClusterPluginList) []*v1.KongClusterPlugin {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1.KongClusterPluginList, items []*v1.KongClusterPlugin) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
