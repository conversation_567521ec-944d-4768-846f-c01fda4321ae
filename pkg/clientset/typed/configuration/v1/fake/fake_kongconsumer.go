/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1 "github.com/kong/kubernetes-configuration/api/configuration/v1"
	configurationv1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongConsumers implements KongConsumerInterface
type fakeKongConsumers struct {
	*gentype.FakeClientWithList[*v1.KongConsumer, *v1.KongConsumerList]
	Fake *FakeConfigurationV1
}

func newFakeKongConsumers(fake *FakeConfigurationV1, namespace string) configurationv1.KongConsumerInterface {
	return &fakeKongConsumers{
		gentype.NewFakeClientWithList[*v1.KongConsumer, *v1.KongConsumerList](
			fake.Fake,
			namespace,
			v1.SchemeGroupVersion.WithResource("kongconsumers"),
			v1.SchemeGroupVersion.WithKind("KongConsumer"),
			func() *v1.KongConsumer { return &v1.KongConsumer{} },
			func() *v1.KongConsumerList { return &v1.KongConsumerList{} },
			func(dst, src *v1.KongConsumerList) { dst.ListMeta = src.ListMeta },
			func(list *v1.KongConsumerList) []*v1.KongConsumer { return gentype.ToPointerSlice(list.Items) },
			func(list *v1.KongConsumerList, items []*v1.KongConsumer) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
