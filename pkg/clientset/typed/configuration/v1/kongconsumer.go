/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	context "context"

	configurationv1 "github.com/kong/kubernetes-configuration/api/configuration/v1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KongConsumersGetter has a method to return a KongConsumerInterface.
// A group's client should implement this interface.
type KongConsumersGetter interface {
	KongConsumers(namespace string) KongConsumerInterface
}

// KongConsumerInterface has methods to work with KongConsumer resources.
type KongConsumerInterface interface {
	Create(ctx context.Context, kongConsumer *configurationv1.KongConsumer, opts metav1.CreateOptions) (*configurationv1.KongConsumer, error)
	Update(ctx context.Context, kongConsumer *configurationv1.KongConsumer, opts metav1.UpdateOptions) (*configurationv1.KongConsumer, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, kongConsumer *configurationv1.KongConsumer, opts metav1.UpdateOptions) (*configurationv1.KongConsumer, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*configurationv1.KongConsumer, error)
	List(ctx context.Context, opts metav1.ListOptions) (*configurationv1.KongConsumerList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *configurationv1.KongConsumer, err error)
	KongConsumerExpansion
}

// kongConsumers implements KongConsumerInterface
type kongConsumers struct {
	*gentype.ClientWithList[*configurationv1.KongConsumer, *configurationv1.KongConsumerList]
}

// newKongConsumers returns a KongConsumers
func newKongConsumers(c *ConfigurationV1Client, namespace string) *kongConsumers {
	return &kongConsumers{
		gentype.NewClientWithList[*configurationv1.KongConsumer, *configurationv1.KongConsumerList](
			"kongconsumers",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *configurationv1.KongConsumer { return &configurationv1.KongConsumer{} },
			func() *configurationv1.KongConsumerList { return &configurationv1.KongConsumerList{} },
		),
	}
}
