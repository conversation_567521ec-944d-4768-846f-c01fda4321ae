/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1beta1 "github.com/kong/kubernetes-configuration/api/configuration/v1beta1"
	configurationv1beta1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1beta1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongUpstreamPolicies implements KongUpstreamPolicyInterface
type fakeKongUpstreamPolicies struct {
	*gentype.FakeClientWithList[*v1beta1.KongUpstreamPolicy, *v1beta1.KongUpstreamPolicyList]
	Fake *FakeConfigurationV1beta1
}

func newFakeKongUpstreamPolicies(fake *FakeConfigurationV1beta1, namespace string) configurationv1beta1.KongUpstreamPolicyInterface {
	return &fakeKongUpstreamPolicies{
		gentype.NewFakeClientWithList[*v1beta1.KongUpstreamPolicy, *v1beta1.KongUpstreamPolicyList](
			fake.Fake,
			namespace,
			v1beta1.SchemeGroupVersion.WithResource("kongupstreampolicies"),
			v1beta1.SchemeGroupVersion.WithKind("KongUpstreamPolicy"),
			func() *v1beta1.KongUpstreamPolicy { return &v1beta1.KongUpstreamPolicy{} },
			func() *v1beta1.KongUpstreamPolicyList { return &v1beta1.KongUpstreamPolicyList{} },
			func(dst, src *v1beta1.KongUpstreamPolicyList) { dst.ListMeta = src.ListMeta },
			func(list *v1beta1.KongUpstreamPolicyList) []*v1beta1.KongUpstreamPolicy {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1beta1.KongUpstreamPolicyList, items []*v1beta1.KongUpstreamPolicy) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
