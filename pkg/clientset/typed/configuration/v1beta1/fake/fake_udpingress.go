/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1beta1 "github.com/kong/kubernetes-configuration/api/configuration/v1beta1"
	configurationv1beta1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1beta1"
	gentype "k8s.io/client-go/gentype"
)

// fakeUDPIngresses implements UDPIngressInterface
type fakeUDPIngresses struct {
	*gentype.FakeClientWithList[*v1beta1.UDPIngress, *v1beta1.UDPIngressList]
	Fake *FakeConfigurationV1beta1
}

func newFakeUDPIngresses(fake *FakeConfigurationV1beta1, namespace string) configurationv1beta1.UDPIngressInterface {
	return &fakeUDPIngresses{
		gentype.NewFakeClientWithList[*v1beta1.UDPIngress, *v1beta1.UDPIngressList](
			fake.Fake,
			namespace,
			v1beta1.SchemeGroupVersion.WithResource("udpingresses"),
			v1beta1.SchemeGroupVersion.WithKind("UDPIngress"),
			func() *v1beta1.UDPIngress { return &v1beta1.UDPIngress{} },
			func() *v1beta1.UDPIngressList { return &v1beta1.UDPIngressList{} },
			func(dst, src *v1beta1.UDPIngressList) { dst.ListMeta = src.ListMeta },
			func(list *v1beta1.UDPIngressList) []*v1beta1.UDPIngress { return gentype.ToPointerSlice(list.Items) },
			func(list *v1beta1.UDPIngressList, items []*v1beta1.UDPIngress) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
