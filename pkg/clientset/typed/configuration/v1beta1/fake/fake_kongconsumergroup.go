/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1beta1 "github.com/kong/kubernetes-configuration/api/configuration/v1beta1"
	configurationv1beta1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1beta1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongConsumerGroups implements KongConsumerGroupInterface
type fakeKongConsumerGroups struct {
	*gentype.FakeClientWithList[*v1beta1.KongConsumerGroup, *v1beta1.KongConsumerGroupList]
	Fake *FakeConfigurationV1beta1
}

func newFakeKongConsumerGroups(fake *FakeConfigurationV1beta1, namespace string) configurationv1beta1.KongConsumerGroupInterface {
	return &fakeKongConsumerGroups{
		gentype.NewFakeClientWithList[*v1beta1.KongConsumerGroup, *v1beta1.KongConsumerGroupList](
			fake.Fake,
			namespace,
			v1beta1.SchemeGroupVersion.WithResource("kongconsumergroups"),
			v1beta1.SchemeGroupVersion.WithKind("KongConsumerGroup"),
			func() *v1beta1.KongConsumerGroup { return &v1beta1.KongConsumerGroup{} },
			func() *v1beta1.KongConsumerGroupList { return &v1beta1.KongConsumerGroupList{} },
			func(dst, src *v1beta1.KongConsumerGroupList) { dst.ListMeta = src.ListMeta },
			func(list *v1beta1.KongConsumerGroupList) []*v1beta1.KongConsumerGroup {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1beta1.KongConsumerGroupList, items []*v1beta1.KongConsumerGroup) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
