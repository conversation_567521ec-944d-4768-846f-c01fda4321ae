/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	context "context"

	configurationv1beta1 "github.com/kong/kubernetes-configuration/api/configuration/v1beta1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KongUpstreamPoliciesGetter has a method to return a KongUpstreamPolicyInterface.
// A group's client should implement this interface.
type KongUpstreamPoliciesGetter interface {
	KongUpstreamPolicies(namespace string) KongUpstreamPolicyInterface
}

// KongUpstreamPolicyInterface has methods to work with KongUpstreamPolicy resources.
type KongUpstreamPolicyInterface interface {
	Create(ctx context.Context, kongUpstreamPolicy *configurationv1beta1.KongUpstreamPolicy, opts v1.CreateOptions) (*configurationv1beta1.KongUpstreamPolicy, error)
	Update(ctx context.Context, kongUpstreamPolicy *configurationv1beta1.KongUpstreamPolicy, opts v1.UpdateOptions) (*configurationv1beta1.KongUpstreamPolicy, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, kongUpstreamPolicy *configurationv1beta1.KongUpstreamPolicy, opts v1.UpdateOptions) (*configurationv1beta1.KongUpstreamPolicy, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*configurationv1beta1.KongUpstreamPolicy, error)
	List(ctx context.Context, opts v1.ListOptions) (*configurationv1beta1.KongUpstreamPolicyList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *configurationv1beta1.KongUpstreamPolicy, err error)
	KongUpstreamPolicyExpansion
}

// kongUpstreamPolicies implements KongUpstreamPolicyInterface
type kongUpstreamPolicies struct {
	*gentype.ClientWithList[*configurationv1beta1.KongUpstreamPolicy, *configurationv1beta1.KongUpstreamPolicyList]
}

// newKongUpstreamPolicies returns a KongUpstreamPolicies
func newKongUpstreamPolicies(c *ConfigurationV1beta1Client, namespace string) *kongUpstreamPolicies {
	return &kongUpstreamPolicies{
		gentype.NewClientWithList[*configurationv1beta1.KongUpstreamPolicy, *configurationv1beta1.KongUpstreamPolicyList](
			"kongupstreampolicies",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *configurationv1beta1.KongUpstreamPolicy { return &configurationv1beta1.KongUpstreamPolicy{} },
			func() *configurationv1beta1.KongUpstreamPolicyList {
				return &configurationv1beta1.KongUpstreamPolicyList{}
			},
		),
	}
}
