/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	context "context"

	configurationv1beta1 "github.com/kong/kubernetes-configuration/api/configuration/v1beta1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KongConsumerGroupsGetter has a method to return a KongConsumerGroupInterface.
// A group's client should implement this interface.
type KongConsumerGroupsGetter interface {
	KongConsumerGroups(namespace string) KongConsumerGroupInterface
}

// KongConsumerGroupInterface has methods to work with KongConsumerGroup resources.
type KongConsumerGroupInterface interface {
	Create(ctx context.Context, kongConsumerGroup *configurationv1beta1.KongConsumerGroup, opts v1.CreateOptions) (*configurationv1beta1.KongConsumerGroup, error)
	Update(ctx context.Context, kongConsumerGroup *configurationv1beta1.KongConsumerGroup, opts v1.UpdateOptions) (*configurationv1beta1.KongConsumerGroup, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, kongConsumerGroup *configurationv1beta1.KongConsumerGroup, opts v1.UpdateOptions) (*configurationv1beta1.KongConsumerGroup, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*configurationv1beta1.KongConsumerGroup, error)
	List(ctx context.Context, opts v1.ListOptions) (*configurationv1beta1.KongConsumerGroupList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *configurationv1beta1.KongConsumerGroup, err error)
	KongConsumerGroupExpansion
}

// kongConsumerGroups implements KongConsumerGroupInterface
type kongConsumerGroups struct {
	*gentype.ClientWithList[*configurationv1beta1.KongConsumerGroup, *configurationv1beta1.KongConsumerGroupList]
}

// newKongConsumerGroups returns a KongConsumerGroups
func newKongConsumerGroups(c *ConfigurationV1beta1Client, namespace string) *kongConsumerGroups {
	return &kongConsumerGroups{
		gentype.NewClientWithList[*configurationv1beta1.KongConsumerGroup, *configurationv1beta1.KongConsumerGroupList](
			"kongconsumergroups",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *configurationv1beta1.KongConsumerGroup { return &configurationv1beta1.KongConsumerGroup{} },
			func() *configurationv1beta1.KongConsumerGroupList {
				return &configurationv1beta1.KongConsumerGroupList{}
			},
		),
	}
}
