/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	configurationv1alpha1 "github.com/kong/kubernetes-configuration/api/configuration/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// IngressClassParametersesGetter has a method to return a IngressClassParametersInterface.
// A group's client should implement this interface.
type IngressClassParametersesGetter interface {
	IngressClassParameterses(namespace string) IngressClassParametersInterface
}

// IngressClassParametersInterface has methods to work with IngressClassParameters resources.
type IngressClassParametersInterface interface {
	Create(ctx context.Context, ingressClassParameters *configurationv1alpha1.IngressClassParameters, opts v1.CreateOptions) (*configurationv1alpha1.IngressClassParameters, error)
	Update(ctx context.Context, ingressClassParameters *configurationv1alpha1.IngressClassParameters, opts v1.UpdateOptions) (*configurationv1alpha1.IngressClassParameters, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*configurationv1alpha1.IngressClassParameters, error)
	List(ctx context.Context, opts v1.ListOptions) (*configurationv1alpha1.IngressClassParametersList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *configurationv1alpha1.IngressClassParameters, err error)
	IngressClassParametersExpansion
}

// ingressClassParameterses implements IngressClassParametersInterface
type ingressClassParameterses struct {
	*gentype.ClientWithList[*configurationv1alpha1.IngressClassParameters, *configurationv1alpha1.IngressClassParametersList]
}

// newIngressClassParameterses returns a IngressClassParameterses
func newIngressClassParameterses(c *ConfigurationV1alpha1Client, namespace string) *ingressClassParameterses {
	return &ingressClassParameterses{
		gentype.NewClientWithList[*configurationv1alpha1.IngressClassParameters, *configurationv1alpha1.IngressClassParametersList](
			"ingressclassparameterses",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *configurationv1alpha1.IngressClassParameters {
				return &configurationv1alpha1.IngressClassParameters{}
			},
			func() *configurationv1alpha1.IngressClassParametersList {
				return &configurationv1alpha1.IngressClassParametersList{}
			},
		),
	}
}
