/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	configurationv1alpha1 "github.com/kong/kubernetes-configuration/api/configuration/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KongVaultsGetter has a method to return a KongVaultInterface.
// A group's client should implement this interface.
type KongVaultsGetter interface {
	KongVaults() KongVaultInterface
}

// KongVaultInterface has methods to work with KongVault resources.
type KongVaultInterface interface {
	Create(ctx context.Context, kongVault *configurationv1alpha1.KongVault, opts v1.CreateOptions) (*configurationv1alpha1.KongVault, error)
	Update(ctx context.Context, kongVault *configurationv1alpha1.KongVault, opts v1.UpdateOptions) (*configurationv1alpha1.KongVault, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, kongVault *configurationv1alpha1.KongVault, opts v1.UpdateOptions) (*configurationv1alpha1.KongVault, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*configurationv1alpha1.KongVault, error)
	List(ctx context.Context, opts v1.ListOptions) (*configurationv1alpha1.KongVaultList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *configurationv1alpha1.KongVault, err error)
	KongVaultExpansion
}

// kongVaults implements KongVaultInterface
type kongVaults struct {
	*gentype.ClientWithList[*configurationv1alpha1.KongVault, *configurationv1alpha1.KongVaultList]
}

// newKongVaults returns a KongVaults
func newKongVaults(c *ConfigurationV1alpha1Client) *kongVaults {
	return &kongVaults{
		gentype.NewClientWithList[*configurationv1alpha1.KongVault, *configurationv1alpha1.KongVaultList](
			"kongvaults",
			c.RESTClient(),
			scheme.ParameterCodec,
			"",
			func() *configurationv1alpha1.KongVault { return &configurationv1alpha1.KongVault{} },
			func() *configurationv1alpha1.KongVaultList { return &configurationv1alpha1.KongVaultList{} },
		),
	}
}
