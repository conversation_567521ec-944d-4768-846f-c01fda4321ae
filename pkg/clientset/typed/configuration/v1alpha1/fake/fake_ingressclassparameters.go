/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/configuration/v1alpha1"
	configurationv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeIngressClassParameterses implements IngressClassParametersInterface
type fakeIngressClassParameterses struct {
	*gentype.FakeClientWithList[*v1alpha1.IngressClassParameters, *v1alpha1.IngressClassParametersList]
	Fake *FakeConfigurationV1alpha1
}

func newFakeIngressClassParameterses(fake *FakeConfigurationV1alpha1, namespace string) configurationv1alpha1.IngressClassParametersInterface {
	return &fakeIngressClassParameterses{
		gentype.NewFakeClientWithList[*v1alpha1.IngressClassParameters, *v1alpha1.IngressClassParametersList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("ingressclassparameterses"),
			v1alpha1.SchemeGroupVersion.WithKind("IngressClassParameters"),
			func() *v1alpha1.IngressClassParameters { return &v1alpha1.IngressClassParameters{} },
			func() *v1alpha1.IngressClassParametersList { return &v1alpha1.IngressClassParametersList{} },
			func(dst, src *v1alpha1.IngressClassParametersList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.IngressClassParametersList) []*v1alpha1.IngressClassParameters {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.IngressClassParametersList, items []*v1alpha1.IngressClassParameters) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
