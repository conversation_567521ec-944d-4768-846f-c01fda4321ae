/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1alpha1"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeConfigurationV1alpha1 struct {
	*testing.Fake
}

func (c *FakeConfigurationV1alpha1) IngressClassParameterses(namespace string) v1alpha1.IngressClassParametersInterface {
	return newFakeIngressClassParameterses(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCACertificates(namespace string) v1alpha1.KongCACertificateInterface {
	return newFakeKongCACertificates(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCertificates(namespace string) v1alpha1.KongCertificateInterface {
	return newFakeKongCertificates(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCredentialACLs(namespace string) v1alpha1.KongCredentialACLInterface {
	return newFakeKongCredentialACLs(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCredentialAPIKeys(namespace string) v1alpha1.KongCredentialAPIKeyInterface {
	return newFakeKongCredentialAPIKeys(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCredentialBasicAuths(namespace string) v1alpha1.KongCredentialBasicAuthInterface {
	return newFakeKongCredentialBasicAuths(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCredentialHMACs(namespace string) v1alpha1.KongCredentialHMACInterface {
	return newFakeKongCredentialHMACs(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCredentialJWTs(namespace string) v1alpha1.KongCredentialJWTInterface {
	return newFakeKongCredentialJWTs(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongCustomEntities(namespace string) v1alpha1.KongCustomEntityInterface {
	return newFakeKongCustomEntities(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongDataPlaneClientCertificates(namespace string) v1alpha1.KongDataPlaneClientCertificateInterface {
	return newFakeKongDataPlaneClientCertificates(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongKeys(namespace string) v1alpha1.KongKeyInterface {
	return newFakeKongKeys(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongKeySets(namespace string) v1alpha1.KongKeySetInterface {
	return newFakeKongKeySets(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongLicenses() v1alpha1.KongLicenseInterface {
	return newFakeKongLicenses(c)
}

func (c *FakeConfigurationV1alpha1) KongPluginBindings(namespace string) v1alpha1.KongPluginBindingInterface {
	return newFakeKongPluginBindings(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongRoutes(namespace string) v1alpha1.KongRouteInterface {
	return newFakeKongRoutes(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongSNIs(namespace string) v1alpha1.KongSNIInterface {
	return newFakeKongSNIs(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongServices(namespace string) v1alpha1.KongServiceInterface {
	return newFakeKongServices(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongTargets(namespace string) v1alpha1.KongTargetInterface {
	return newFakeKongTargets(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongUpstreams(namespace string) v1alpha1.KongUpstreamInterface {
	return newFakeKongUpstreams(c, namespace)
}

func (c *FakeConfigurationV1alpha1) KongVaults() v1alpha1.KongVaultInterface {
	return newFakeKongVaults(c)
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeConfigurationV1alpha1) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
