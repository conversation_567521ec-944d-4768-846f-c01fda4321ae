/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/configuration/v1alpha1"
	configurationv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/configuration/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongKeys implements KongKeyInterface
type fakeKongKeys struct {
	*gentype.FakeClientWithList[*v1alpha1.Kong<PERSON>ey, *v1alpha1.KongKeyList]
	Fake *FakeConfigurationV1alpha1
}

func newFakeKongKeys(fake *FakeConfigurationV1alpha1, namespace string) configurationv1alpha1.KongKeyInterface {
	return &fakeKongKeys{
		gentype.NewFakeClientWithList[*v1alpha1.KongKey, *v1alpha1.KongKeyList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("kongkeys"),
			v1alpha1.SchemeGroupVersion.WithKind("KongKey"),
			func() *v1alpha1.KongKey { return &v1alpha1.KongKey{} },
			func() *v1alpha1.KongKeyList { return &v1alpha1.KongKeyList{} },
			func(dst, src *v1alpha1.KongKeyList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.KongKeyList) []*v1alpha1.KongKey { return gentype.ToPointerSlice(list.Items) },
			func(list *v1alpha1.KongKeyList, items []*v1alpha1.KongKey) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
