/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	configurationv1alpha1 "github.com/kong/kubernetes-configuration/api/configuration/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KongDataPlaneClientCertificatesGetter has a method to return a KongDataPlaneClientCertificateInterface.
// A group's client should implement this interface.
type KongDataPlaneClientCertificatesGetter interface {
	KongDataPlaneClientCertificates(namespace string) KongDataPlaneClientCertificateInterface
}

// KongDataPlaneClientCertificateInterface has methods to work with KongDataPlaneClientCertificate resources.
type KongDataPlaneClientCertificateInterface interface {
	Create(ctx context.Context, kongDataPlaneClientCertificate *configurationv1alpha1.KongDataPlaneClientCertificate, opts v1.CreateOptions) (*configurationv1alpha1.KongDataPlaneClientCertificate, error)
	Update(ctx context.Context, kongDataPlaneClientCertificate *configurationv1alpha1.KongDataPlaneClientCertificate, opts v1.UpdateOptions) (*configurationv1alpha1.KongDataPlaneClientCertificate, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, kongDataPlaneClientCertificate *configurationv1alpha1.KongDataPlaneClientCertificate, opts v1.UpdateOptions) (*configurationv1alpha1.KongDataPlaneClientCertificate, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*configurationv1alpha1.KongDataPlaneClientCertificate, error)
	List(ctx context.Context, opts v1.ListOptions) (*configurationv1alpha1.KongDataPlaneClientCertificateList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *configurationv1alpha1.KongDataPlaneClientCertificate, err error)
	KongDataPlaneClientCertificateExpansion
}

// kongDataPlaneClientCertificates implements KongDataPlaneClientCertificateInterface
type kongDataPlaneClientCertificates struct {
	*gentype.ClientWithList[*configurationv1alpha1.KongDataPlaneClientCertificate, *configurationv1alpha1.KongDataPlaneClientCertificateList]
}

// newKongDataPlaneClientCertificates returns a KongDataPlaneClientCertificates
func newKongDataPlaneClientCertificates(c *ConfigurationV1alpha1Client, namespace string) *kongDataPlaneClientCertificates {
	return &kongDataPlaneClientCertificates{
		gentype.NewClientWithList[*configurationv1alpha1.KongDataPlaneClientCertificate, *configurationv1alpha1.KongDataPlaneClientCertificateList](
			"kongdataplaneclientcertificates",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *configurationv1alpha1.KongDataPlaneClientCertificate {
				return &configurationv1alpha1.KongDataPlaneClientCertificate{}
			},
			func() *configurationv1alpha1.KongDataPlaneClientCertificateList {
				return &configurationv1alpha1.KongDataPlaneClientCertificateList{}
			},
		),
	}
}
