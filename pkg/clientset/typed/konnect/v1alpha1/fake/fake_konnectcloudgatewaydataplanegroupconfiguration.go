/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/konnect/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKonnectCloudGatewayDataPlaneGroupConfigurations implements KonnectCloudGatewayDataPlaneGroupConfigurationInterface
type fakeKonnectCloudGatewayDataPlaneGroupConfigurations struct {
	*gentype.FakeClientWithList[*v1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList]
	Fake *FakeKonnectV1alpha1
}

func newFakeKonnectCloudGatewayDataPlaneGroupConfigurations(fake *FakeKonnectV1alpha1, namespace string) konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationInterface {
	return &fakeKonnectCloudGatewayDataPlaneGroupConfigurations{
		gentype.NewFakeClientWithList[*v1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("konnectcloudgatewaydataplanegroupconfigurations"),
			v1alpha1.SchemeGroupVersion.WithKind("KonnectCloudGatewayDataPlaneGroupConfiguration"),
			func() *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration {
				return &v1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration{}
			},
			func() *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList {
				return &v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList{}
			},
			func(dst, src *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList) {
				dst.ListMeta = src.ListMeta
			},
			func(list *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList) []*v1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList, items []*v1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
