/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/konnect/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKonnectCloudGatewayNetworks implements KonnectCloudGatewayNetworkInterface
type fakeKonnectCloudGatewayNetworks struct {
	*gentype.FakeClientWithList[*v1alpha1.KonnectCloudGatewayNetwork, *v1alpha1.KonnectCloudGatewayNetworkList]
	Fake *FakeKonnectV1alpha1
}

func newFakeKonnectCloudGatewayNetworks(fake *FakeKonnectV1alpha1, namespace string) konnectv1alpha1.KonnectCloudGatewayNetworkInterface {
	return &fakeKonnectCloudGatewayNetworks{
		gentype.NewFakeClientWithList[*v1alpha1.KonnectCloudGatewayNetwork, *v1alpha1.KonnectCloudGatewayNetworkList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("konnectcloudgatewaynetworks"),
			v1alpha1.SchemeGroupVersion.WithKind("KonnectCloudGatewayNetwork"),
			func() *v1alpha1.KonnectCloudGatewayNetwork { return &v1alpha1.KonnectCloudGatewayNetwork{} },
			func() *v1alpha1.KonnectCloudGatewayNetworkList { return &v1alpha1.KonnectCloudGatewayNetworkList{} },
			func(dst, src *v1alpha1.KonnectCloudGatewayNetworkList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.KonnectCloudGatewayNetworkList) []*v1alpha1.KonnectCloudGatewayNetwork {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.KonnectCloudGatewayNetworkList, items []*v1alpha1.KonnectCloudGatewayNetwork) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
