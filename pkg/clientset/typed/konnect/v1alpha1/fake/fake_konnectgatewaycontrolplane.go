/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
	konnectv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/konnect/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKonnectGatewayControlPlanes implements KonnectGatewayControlPlaneInterface
type fakeKonnectGatewayControlPlanes struct {
	*gentype.FakeClientWithList[*v1alpha1.KonnectGatewayControlPlane, *v1alpha1.KonnectGatewayControlPlaneList]
	Fake *FakeKonnectV1alpha1
}

func newFakeKonnectGatewayControlPlanes(fake *FakeKonnectV1alpha1, namespace string) konnectv1alpha1.KonnectGatewayControlPlaneInterface {
	return &fakeKonnectGatewayControlPlanes{
		gentype.NewFakeClientWithList[*v1alpha1.KonnectGatewayControlPlane, *v1alpha1.KonnectGatewayControlPlaneList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("konnectgatewaycontrolplanes"),
			v1alpha1.SchemeGroupVersion.WithKind("KonnectGatewayControlPlane"),
			func() *v1alpha1.KonnectGatewayControlPlane { return &v1alpha1.KonnectGatewayControlPlane{} },
			func() *v1alpha1.KonnectGatewayControlPlaneList { return &v1alpha1.KonnectGatewayControlPlaneList{} },
			func(dst, src *v1alpha1.KonnectGatewayControlPlaneList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.KonnectGatewayControlPlaneList) []*v1alpha1.KonnectGatewayControlPlane {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.KonnectGatewayControlPlaneList, items []*v1alpha1.KonnectGatewayControlPlane) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
