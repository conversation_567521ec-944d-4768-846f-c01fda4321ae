/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	konnectv1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KonnectCloudGatewayDataPlaneGroupConfigurationsGetter has a method to return a KonnectCloudGatewayDataPlaneGroupConfigurationInterface.
// A group's client should implement this interface.
type KonnectCloudGatewayDataPlaneGroupConfigurationsGetter interface {
	KonnectCloudGatewayDataPlaneGroupConfigurations(namespace string) KonnectCloudGatewayDataPlaneGroupConfigurationInterface
}

// KonnectCloudGatewayDataPlaneGroupConfigurationInterface has methods to work with KonnectCloudGatewayDataPlaneGroupConfiguration resources.
type KonnectCloudGatewayDataPlaneGroupConfigurationInterface interface {
	Create(ctx context.Context, konnectCloudGatewayDataPlaneGroupConfiguration *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, opts v1.CreateOptions) (*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, error)
	Update(ctx context.Context, konnectCloudGatewayDataPlaneGroupConfiguration *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, opts v1.UpdateOptions) (*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, konnectCloudGatewayDataPlaneGroupConfiguration *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, opts v1.UpdateOptions) (*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, error)
	List(ctx context.Context, opts v1.ListOptions) (*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, err error)
	KonnectCloudGatewayDataPlaneGroupConfigurationExpansion
}

// konnectCloudGatewayDataPlaneGroupConfigurations implements KonnectCloudGatewayDataPlaneGroupConfigurationInterface
type konnectCloudGatewayDataPlaneGroupConfigurations struct {
	*gentype.ClientWithList[*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList]
}

// newKonnectCloudGatewayDataPlaneGroupConfigurations returns a KonnectCloudGatewayDataPlaneGroupConfigurations
func newKonnectCloudGatewayDataPlaneGroupConfigurations(c *KonnectV1alpha1Client, namespace string) *konnectCloudGatewayDataPlaneGroupConfigurations {
	return &konnectCloudGatewayDataPlaneGroupConfigurations{
		gentype.NewClientWithList[*konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration, *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList](
			"konnectcloudgatewaydataplanegroupconfigurations",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration {
				return &konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfiguration{}
			},
			func() *konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList {
				return &konnectv1alpha1.KonnectCloudGatewayDataPlaneGroupConfigurationList{}
			},
		),
	}
}
