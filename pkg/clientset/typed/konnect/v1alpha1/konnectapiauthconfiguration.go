/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	konnectv1alpha1 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KonnectAPIAuthConfigurationsGetter has a method to return a KonnectAPIAuthConfigurationInterface.
// A group's client should implement this interface.
type KonnectAPIAuthConfigurationsGetter interface {
	KonnectAPIAuthConfigurations(namespace string) KonnectAPIAuthConfigurationInterface
}

// KonnectAPIAuthConfigurationInterface has methods to work with KonnectAPIAuthConfiguration resources.
type KonnectAPIAuthConfigurationInterface interface {
	Create(ctx context.Context, konnectAPIAuthConfiguration *konnectv1alpha1.KonnectAPIAuthConfiguration, opts v1.CreateOptions) (*konnectv1alpha1.KonnectAPIAuthConfiguration, error)
	Update(ctx context.Context, konnectAPIAuthConfiguration *konnectv1alpha1.KonnectAPIAuthConfiguration, opts v1.UpdateOptions) (*konnectv1alpha1.KonnectAPIAuthConfiguration, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, konnectAPIAuthConfiguration *konnectv1alpha1.KonnectAPIAuthConfiguration, opts v1.UpdateOptions) (*konnectv1alpha1.KonnectAPIAuthConfiguration, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*konnectv1alpha1.KonnectAPIAuthConfiguration, error)
	List(ctx context.Context, opts v1.ListOptions) (*konnectv1alpha1.KonnectAPIAuthConfigurationList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *konnectv1alpha1.KonnectAPIAuthConfiguration, err error)
	KonnectAPIAuthConfigurationExpansion
}

// konnectAPIAuthConfigurations implements KonnectAPIAuthConfigurationInterface
type konnectAPIAuthConfigurations struct {
	*gentype.ClientWithList[*konnectv1alpha1.KonnectAPIAuthConfiguration, *konnectv1alpha1.KonnectAPIAuthConfigurationList]
}

// newKonnectAPIAuthConfigurations returns a KonnectAPIAuthConfigurations
func newKonnectAPIAuthConfigurations(c *KonnectV1alpha1Client, namespace string) *konnectAPIAuthConfigurations {
	return &konnectAPIAuthConfigurations{
		gentype.NewClientWithList[*konnectv1alpha1.KonnectAPIAuthConfiguration, *konnectv1alpha1.KonnectAPIAuthConfigurationList](
			"konnectapiauthconfigurations",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *konnectv1alpha1.KonnectAPIAuthConfiguration {
				return &konnectv1alpha1.KonnectAPIAuthConfiguration{}
			},
			func() *konnectv1alpha1.KonnectAPIAuthConfigurationList {
				return &konnectv1alpha1.KonnectAPIAuthConfigurationList{}
			},
		),
	}
}
