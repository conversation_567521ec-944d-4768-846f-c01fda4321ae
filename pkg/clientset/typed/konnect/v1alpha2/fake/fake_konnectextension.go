/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha2 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha2"
	konnectv1alpha2 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/konnect/v1alpha2"
	gentype "k8s.io/client-go/gentype"
)

// fakeKonnectExtensions implements KonnectExtensionInterface
type fakeKonnectExtensions struct {
	*gentype.FakeClientWithList[*v1alpha2.KonnectExtension, *v1alpha2.KonnectExtensionList]
	Fake *FakeKonnectV1alpha2
}

func newFakeKonnectExtensions(fake *FakeKonnectV1alpha2, namespace string) konnectv1alpha2.KonnectExtensionInterface {
	return &fakeKonnectExtensions{
		gentype.NewFakeClientWithList[*v1alpha2.KonnectExtension, *v1alpha2.KonnectExtensionList](
			fake.Fake,
			namespace,
			v1alpha2.SchemeGroupVersion.WithResource("konnectextensions"),
			v1alpha2.SchemeGroupVersion.WithKind("KonnectExtension"),
			func() *v1alpha2.KonnectExtension { return &v1alpha2.KonnectExtension{} },
			func() *v1alpha2.KonnectExtensionList { return &v1alpha2.KonnectExtensionList{} },
			func(dst, src *v1alpha2.KonnectExtensionList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha2.KonnectExtensionList) []*v1alpha2.KonnectExtension {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha2.KonnectExtensionList, items []*v1alpha2.KonnectExtension) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
