/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha2

import (
	context "context"

	konnectv1alpha2 "github.com/kong/kubernetes-configuration/api/konnect/v1alpha2"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// KonnectExtensionsGetter has a method to return a KonnectExtensionInterface.
// A group's client should implement this interface.
type KonnectExtensionsGetter interface {
	KonnectExtensions(namespace string) KonnectExtensionInterface
}

// KonnectExtensionInterface has methods to work with KonnectExtension resources.
type KonnectExtensionInterface interface {
	Create(ctx context.Context, konnectExtension *konnectv1alpha2.KonnectExtension, opts v1.CreateOptions) (*konnectv1alpha2.KonnectExtension, error)
	Update(ctx context.Context, konnectExtension *konnectv1alpha2.KonnectExtension, opts v1.UpdateOptions) (*konnectv1alpha2.KonnectExtension, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, konnectExtension *konnectv1alpha2.KonnectExtension, opts v1.UpdateOptions) (*konnectv1alpha2.KonnectExtension, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*konnectv1alpha2.KonnectExtension, error)
	List(ctx context.Context, opts v1.ListOptions) (*konnectv1alpha2.KonnectExtensionList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *konnectv1alpha2.KonnectExtension, err error)
	KonnectExtensionExpansion
}

// konnectExtensions implements KonnectExtensionInterface
type konnectExtensions struct {
	*gentype.ClientWithList[*konnectv1alpha2.KonnectExtension, *konnectv1alpha2.KonnectExtensionList]
}

// newKonnectExtensions returns a KonnectExtensions
func newKonnectExtensions(c *KonnectV1alpha2Client, namespace string) *konnectExtensions {
	return &konnectExtensions{
		gentype.NewClientWithList[*konnectv1alpha2.KonnectExtension, *konnectv1alpha2.KonnectExtensionList](
			"konnectextensions",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *konnectv1alpha2.KonnectExtension { return &konnectv1alpha2.KonnectExtension{} },
			func() *konnectv1alpha2.KonnectExtensionList { return &konnectv1alpha2.KonnectExtensionList{} },
		),
	}
}
