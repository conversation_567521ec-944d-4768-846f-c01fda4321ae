/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	gatewayoperatorv1alpha1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// WatchNamespaceGrantsGetter has a method to return a WatchNamespaceGrantInterface.
// A group's client should implement this interface.
type WatchNamespaceGrantsGetter interface {
	WatchNamespaceGrants(namespace string) WatchNamespaceGrantInterface
}

// WatchNamespaceGrantInterface has methods to work with WatchNamespaceGrant resources.
type WatchNamespaceGrantInterface interface {
	Create(ctx context.Context, watchNamespaceGrant *gatewayoperatorv1alpha1.WatchNamespaceGrant, opts v1.CreateOptions) (*gatewayoperatorv1alpha1.WatchNamespaceGrant, error)
	Update(ctx context.Context, watchNamespaceGrant *gatewayoperatorv1alpha1.WatchNamespaceGrant, opts v1.UpdateOptions) (*gatewayoperatorv1alpha1.WatchNamespaceGrant, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*gatewayoperatorv1alpha1.WatchNamespaceGrant, error)
	List(ctx context.Context, opts v1.ListOptions) (*gatewayoperatorv1alpha1.WatchNamespaceGrantList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *gatewayoperatorv1alpha1.WatchNamespaceGrant, err error)
	WatchNamespaceGrantExpansion
}

// watchNamespaceGrants implements WatchNamespaceGrantInterface
type watchNamespaceGrants struct {
	*gentype.ClientWithList[*gatewayoperatorv1alpha1.WatchNamespaceGrant, *gatewayoperatorv1alpha1.WatchNamespaceGrantList]
}

// newWatchNamespaceGrants returns a WatchNamespaceGrants
func newWatchNamespaceGrants(c *GatewayOperatorV1alpha1Client, namespace string) *watchNamespaceGrants {
	return &watchNamespaceGrants{
		gentype.NewClientWithList[*gatewayoperatorv1alpha1.WatchNamespaceGrant, *gatewayoperatorv1alpha1.WatchNamespaceGrantList](
			"watchnamespacegrants",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *gatewayoperatorv1alpha1.WatchNamespaceGrant {
				return &gatewayoperatorv1alpha1.WatchNamespaceGrant{}
			},
			func() *gatewayoperatorv1alpha1.WatchNamespaceGrantList {
				return &gatewayoperatorv1alpha1.WatchNamespaceGrantList{}
			},
		),
	}
}
