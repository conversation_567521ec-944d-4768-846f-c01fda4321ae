/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	http "net/http"

	gatewayoperatorv1alpha1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1alpha1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	rest "k8s.io/client-go/rest"
)

type GatewayOperatorV1alpha1Interface interface {
	RESTClient() rest.Interface
	AIGatewaysGetter
	DataPlaneMetricsExtensionsGetter
	KongPluginInstallationsGetter
	WatchNamespaceGrantsGetter
}

// GatewayOperatorV1alpha1Client is used to interact with features provided by the gateway-operator.konghq.com group.
type GatewayOperatorV1alpha1Client struct {
	restClient rest.Interface
}

func (c *GatewayOperatorV1alpha1Client) AIGateways(namespace string) AIGatewayInterface {
	return newAIGateways(c, namespace)
}

func (c *GatewayOperatorV1alpha1Client) DataPlaneMetricsExtensions(namespace string) DataPlaneMetricsExtensionInterface {
	return newDataPlaneMetricsExtensions(c, namespace)
}

func (c *GatewayOperatorV1alpha1Client) KongPluginInstallations(namespace string) KongPluginInstallationInterface {
	return newKongPluginInstallations(c, namespace)
}

func (c *GatewayOperatorV1alpha1Client) WatchNamespaceGrants(namespace string) WatchNamespaceGrantInterface {
	return newWatchNamespaceGrants(c, namespace)
}

// NewForConfig creates a new GatewayOperatorV1alpha1Client for the given config.
// NewForConfig is equivalent to NewForConfigAndClient(c, httpClient),
// where httpClient was generated with rest.HTTPClientFor(c).
func NewForConfig(c *rest.Config) (*GatewayOperatorV1alpha1Client, error) {
	config := *c
	setConfigDefaults(&config)
	httpClient, err := rest.HTTPClientFor(&config)
	if err != nil {
		return nil, err
	}
	return NewForConfigAndClient(&config, httpClient)
}

// NewForConfigAndClient creates a new GatewayOperatorV1alpha1Client for the given config and http client.
// Note the http client provided takes precedence over the configured transport values.
func NewForConfigAndClient(c *rest.Config, h *http.Client) (*GatewayOperatorV1alpha1Client, error) {
	config := *c
	setConfigDefaults(&config)
	client, err := rest.RESTClientForConfigAndClient(&config, h)
	if err != nil {
		return nil, err
	}
	return &GatewayOperatorV1alpha1Client{client}, nil
}

// NewForConfigOrDie creates a new GatewayOperatorV1alpha1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *GatewayOperatorV1alpha1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new GatewayOperatorV1alpha1Client for the given RESTClient.
func New(c rest.Interface) *GatewayOperatorV1alpha1Client {
	return &GatewayOperatorV1alpha1Client{c}
}

func setConfigDefaults(config *rest.Config) {
	gv := gatewayoperatorv1alpha1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = rest.CodecFactoryForGeneratedClient(scheme.Scheme, scheme.Codecs).WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *GatewayOperatorV1alpha1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
