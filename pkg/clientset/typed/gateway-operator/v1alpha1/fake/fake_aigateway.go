/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1alpha1"
	gatewayoperatorv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeAIGateways implements AIGatewayInterface
type fakeAIGateways struct {
	*gentype.FakeClientWithList[*v1alpha1.AIGateway, *v1alpha1.AIGatewayList]
	Fake *FakeGatewayOperatorV1alpha1
}

func newFakeAIGateways(fake *FakeGatewayOperatorV1alpha1, namespace string) gatewayoperatorv1alpha1.AIGatewayInterface {
	return &fakeAIGateways{
		gentype.NewFakeClientWithList[*v1alpha1.AIGateway, *v1alpha1.AIGatewayList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("aigateways"),
			v1alpha1.SchemeGroupVersion.WithKind("AIGateway"),
			func() *v1alpha1.AIGateway { return &v1alpha1.AIGateway{} },
			func() *v1alpha1.AIGatewayList { return &v1alpha1.AIGatewayList{} },
			func(dst, src *v1alpha1.AIGatewayList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.AIGatewayList) []*v1alpha1.AIGateway { return gentype.ToPointerSlice(list.Items) },
			func(list *v1alpha1.AIGatewayList, items []*v1alpha1.AIGateway) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
