/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1alpha1"
	gatewayoperatorv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeKongPluginInstallations implements KongPluginInstallationInterface
type fakeKongPluginInstallations struct {
	*gentype.FakeClientWithList[*v1alpha1.KongPluginInstallation, *v1alpha1.KongPluginInstallationList]
	Fake *FakeGatewayOperatorV1alpha1
}

func newFakeKongPluginInstallations(fake *FakeGatewayOperatorV1alpha1, namespace string) gatewayoperatorv1alpha1.KongPluginInstallationInterface {
	return &fakeKongPluginInstallations{
		gentype.NewFakeClientWithList[*v1alpha1.KongPluginInstallation, *v1alpha1.KongPluginInstallationList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("kongplugininstallations"),
			v1alpha1.SchemeGroupVersion.WithKind("KongPluginInstallation"),
			func() *v1alpha1.KongPluginInstallation { return &v1alpha1.KongPluginInstallation{} },
			func() *v1alpha1.KongPluginInstallationList { return &v1alpha1.KongPluginInstallationList{} },
			func(dst, src *v1alpha1.KongPluginInstallationList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.KongPluginInstallationList) []*v1alpha1.KongPluginInstallation {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.KongPluginInstallationList, items []*v1alpha1.KongPluginInstallation) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
