/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1alpha1"
	gatewayoperatorv1alpha1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeDataPlaneMetricsExtensions implements DataPlaneMetricsExtensionInterface
type fakeDataPlaneMetricsExtensions struct {
	*gentype.FakeClientWithList[*v1alpha1.DataPlaneMetricsExtension, *v1alpha1.DataPlaneMetricsExtensionList]
	Fake *FakeGatewayOperatorV1alpha1
}

func newFakeDataPlaneMetricsExtensions(fake *FakeGatewayOperatorV1alpha1, namespace string) gatewayoperatorv1alpha1.DataPlaneMetricsExtensionInterface {
	return &fakeDataPlaneMetricsExtensions{
		gentype.NewFakeClientWithList[*v1alpha1.DataPlaneMetricsExtension, *v1alpha1.DataPlaneMetricsExtensionList](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("dataplanemetricsextensions"),
			v1alpha1.SchemeGroupVersion.WithKind("DataPlaneMetricsExtension"),
			func() *v1alpha1.DataPlaneMetricsExtension { return &v1alpha1.DataPlaneMetricsExtension{} },
			func() *v1alpha1.DataPlaneMetricsExtensionList { return &v1alpha1.DataPlaneMetricsExtensionList{} },
			func(dst, src *v1alpha1.DataPlaneMetricsExtensionList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.DataPlaneMetricsExtensionList) []*v1alpha1.DataPlaneMetricsExtension {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.DataPlaneMetricsExtensionList, items []*v1alpha1.DataPlaneMetricsExtension) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
