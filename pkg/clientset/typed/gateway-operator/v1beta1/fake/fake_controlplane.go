/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1beta1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1beta1"
	gatewayoperatorv1beta1 "github.com/kong/kubernetes-configuration/pkg/clientset/typed/gateway-operator/v1beta1"
	gentype "k8s.io/client-go/gentype"
)

// fakeControlPlanes implements ControlPlaneInterface
type fakeControlPlanes struct {
	*gentype.FakeClientWithList[*v1beta1.ControlPlane, *v1beta1.ControlPlaneList]
	Fake *FakeGatewayOperatorV1beta1
}

func newFakeControlPlanes(fake *FakeGatewayOperatorV1beta1, namespace string) gatewayoperatorv1beta1.ControlPlaneInterface {
	return &fakeControlPlanes{
		gentype.NewFakeClientWithList[*v1beta1.ControlPlane, *v1beta1.ControlPlaneList](
			fake.Fake,
			namespace,
			v1beta1.SchemeGroupVersion.WithResource("controlplanes"),
			v1beta1.SchemeGroupVersion.WithKind("ControlPlane"),
			func() *v1beta1.ControlPlane { return &v1beta1.ControlPlane{} },
			func() *v1beta1.ControlPlaneList { return &v1beta1.ControlPlaneList{} },
			func(dst, src *v1beta1.ControlPlaneList) { dst.ListMeta = src.ListMeta },
			func(list *v1beta1.ControlPlaneList) []*v1beta1.ControlPlane {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1beta1.ControlPlaneList, items []*v1beta1.ControlPlane) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
