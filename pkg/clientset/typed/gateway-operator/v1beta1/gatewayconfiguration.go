/*
Copyright 2021 Kong, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	context "context"

	gatewayoperatorv1beta1 "github.com/kong/kubernetes-configuration/api/gateway-operator/v1beta1"
	scheme "github.com/kong/kubernetes-configuration/pkg/clientset/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// GatewayConfigurationsGetter has a method to return a GatewayConfigurationInterface.
// A group's client should implement this interface.
type GatewayConfigurationsGetter interface {
	GatewayConfigurations(namespace string) GatewayConfigurationInterface
}

// GatewayConfigurationInterface has methods to work with GatewayConfiguration resources.
type GatewayConfigurationInterface interface {
	Create(ctx context.Context, gatewayConfiguration *gatewayoperatorv1beta1.GatewayConfiguration, opts v1.CreateOptions) (*gatewayoperatorv1beta1.GatewayConfiguration, error)
	Update(ctx context.Context, gatewayConfiguration *gatewayoperatorv1beta1.GatewayConfiguration, opts v1.UpdateOptions) (*gatewayoperatorv1beta1.GatewayConfiguration, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, gatewayConfiguration *gatewayoperatorv1beta1.GatewayConfiguration, opts v1.UpdateOptions) (*gatewayoperatorv1beta1.GatewayConfiguration, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*gatewayoperatorv1beta1.GatewayConfiguration, error)
	List(ctx context.Context, opts v1.ListOptions) (*gatewayoperatorv1beta1.GatewayConfigurationList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *gatewayoperatorv1beta1.GatewayConfiguration, err error)
	GatewayConfigurationExpansion
}

// gatewayConfigurations implements GatewayConfigurationInterface
type gatewayConfigurations struct {
	*gentype.ClientWithList[*gatewayoperatorv1beta1.GatewayConfiguration, *gatewayoperatorv1beta1.GatewayConfigurationList]
}

// newGatewayConfigurations returns a GatewayConfigurations
func newGatewayConfigurations(c *GatewayOperatorV1beta1Client, namespace string) *gatewayConfigurations {
	return &gatewayConfigurations{
		gentype.NewClientWithList[*gatewayoperatorv1beta1.GatewayConfiguration, *gatewayoperatorv1beta1.GatewayConfigurationList](
			"gatewayconfigurations",
			c.RESTClient(),
			scheme.ParameterCodec,
			namespace,
			func() *gatewayoperatorv1beta1.GatewayConfiguration {
				return &gatewayoperatorv1beta1.GatewayConfiguration{}
			},
			func() *gatewayoperatorv1beta1.GatewayConfigurationList {
				return &gatewayoperatorv1beta1.GatewayConfigurationList{}
			},
		),
	}
}
